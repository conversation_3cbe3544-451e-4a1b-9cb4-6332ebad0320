<!-- image crop Modal -->
<div class="modal fade in" id="image_crop_modal" tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false"
    data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Image crop</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="icbFileName">
                <input type="hidden" id="icbFileType">
                <div class="form-group">
                    <i class="btn btn-info btn-xs fa fa-search-plus" id="zi" data-zoom='0.1'></i>
                    <i class="btn btn-info btn-xs fa fa-search-minus" id="zo" data-zoom="-0.1"></i>
                    <i class="btn btn-info btn-xs fa fa-arrow-left" id="ml" data-move="-20"></i>
                    <i class="btn btn-info btn-xs fa fa-arrow-right" id="mr" data-move="20"></i>
                    <i class="btn btn-info btn-xs fa fa-arrow-up" id="mt" data-move="-20"></i>
                    <i class="btn btn-info btn-xs fa fa-arrow-down" id="mb" data-move="20"></i>
                    <i class="btn btn-info btn-xs fa fa-rotate-left" id="rl" data-rotate="-90"></i>
                    <i class="btn btn-info btn-xs fa fa-rotate-right" id="rr" data-rotate="90"></i>
                    <i class="btn btn-info btn-xs fa fa-arrows-h" id="sx" data-scale="x" data-scaleVal='1'></i>
                    <i class="btn btn-info btn-xs fa fa-arrows-v" id="sy" data-scale="y" data-scaleVal='1'></i>
                    <i class="btn btn-info btn-xs fa fa-refresh" id="reset"></i>
                    <i class="btn btn-success btn-xs fa fa-check" id="crop_image_result">Ok</i>
                </div>
                <div class="img-container">
                    <img id="image_crop_box" src="./assets/img/default-img-crop.png" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    // declare global variables
    var attachments = {
                        primary: {},
                        secondary: []
                    }
    var imageInx;
    var image = document.getElementById('image_crop_box');
    var cropper;
    $(document).ready(function () {
        // zoom in/out
        $('#zi,#zo').click(function () {
            var zoom = $(this).data('zoom');
            cropper.zoom(zoom);
        });
        //move left/right
        $('#ml,#mr').click(function () {
            var move = $(this).data('move');
            cropper.move(move, 0);
        });
        //move bottom/top
        $('#mt,#mb').click(function () {
            var move = $(this).data('move');
            cropper.move(0, move);
        });
        //rotate left/right side
        $('#rl,#rr').click(function () {
            var rotate = $(this).data('rotate');
            cropper.rotate(rotate);
        });
        //scale the image
        $('#sx,#sy').click(function () {
            var scale = $(this).data('scale');
            var scaleVal = $(this).data('scaleVal');
            if (typeof (scaleVal) === 'undefined') {
                scaleVal = '-1';
            }
            scaleVal === '1' ? '-1' : '1';
            if (scale === 'x') {
                cropper.scaleX(scaleVal);
                $(this).data('scaleVal', scaleVal === '1' ? '-1' : '1');
            } else if (scale === 'y') {
                cropper.scaleY(scaleVal);
                $(this).data('scaleVal', scaleVal === '1' ? '-1' : '1');
            }
        });
        //reset the image
        $('#reset').click(function () {
            cropper.reset();
        });
        $('#crop_image_result').click(function () {
            var jk_base64 = cropper.getCroppedCanvas().toDataURL();
            // preview image selector
            // $('img').attr('src', jk_base64);
            $('#image_crop_modal').modal('hide');
            cropper.getCroppedCanvas().toBlob(function (blob) {
                imageInx === 'primary' ?
                    uploadPrimaryDoc(blob) : uploadSecondaryDoc(blob);
            }, 'image/png');
        });

    });
    //get the cropped result
    function getCropResult() {
        $('#image_crop_modal').modal('hide');
        return cropper.getCroppedCanvas().toDataURL();
    }
    // set uploaded type
    function setUploadType(url, inx) {
        imageInx = inx;
        $('#image_crop_modal').on('shown.bs.modal', function () {
            if (cropper) {
                cropper.destroy();
            }
            cropper = new Cropper(image, {
                aspectRatio: NaN,
                // viewMode:1,
                autoCrop: false
            });
            // cropper.replace(`${baseUrl}uploads/${url}`);
            cropper.replace(url);
        }).modal('show');

    }
    function uploadSecondaryDoc(blob) {
        var secondaryCount = Math.floor(Math.random() * 100000);

        //set placeholder
        $('.secondary-upload-preview-img').append(`<div class="seconday-details-${secondaryCount} secondary-count m-2 p-1"><div class="progress"><div class="progress-bar secondary-doc-progress" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div></div></div>`);

        var formData = new FormData();
        formData.append('scan-document', blob);
        formData.append('ind', secondaryCount);
        formData.append('blob', 'yes');
        formData.append('type', 'visa');

        $.ajax({
            url: `${apiUrl}document-details/upload/document`,
            // url: `http://3.110.83.39:3000/v1/api/document-details/upload/document`,
            method: 'POST',
            xhr: function () {
                var myXhr = $.ajaxSettings.xhr();
                if (myXhr.upload) {
                    myXhr.upload.addEventListener('progress', function (e) {
                        if (e.lengthComputable) {
                            var max = e.total;
                            var current = e.loaded;
                            var percentage = ((current * 100) / max).toFixed(2);
                            if (percentage > 10) {
                                $('.seconday-details-' + secondaryCount + ' .secondary-doc-progress').width(`${percentage}%`).text(percentage + ' %').css({ color: '#fff' });
                            } else {
                                $('.seconday-details-' + secondaryCount + ' .secondary-doc-progress').width(`${percentage}%`).text(percentage + ' %').css({ color: '#155adc' });
                            }
                            if (percentage >= 100) {
                                $('.seconday-details-' + secondaryCount + ' .secondary-doc-progress').text('Processing request...');
                            }
                        }
                    }, false);
                }
                return myXhr;
            },
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
            }, success: function (res) {
                console.log("#############IMG RESP",res)
                if (res.status) {
                    attachments['secondary'].push({ fileName: res.fileName, thumbnail: res.thumbnail, ext: res.ext, ind: (res.ind).toString(), thumb: res.genThumbnail, ocrData: res.ocr, isPrimary: false });
                   console.log("############RESIND",res.ind);
                    // if (res.ocr.status === false) {
                        $('.seconday-details-' + res.ind).html(`<a href="${baseUrl + 'uploads/' + res.fileName}" class="passport-scan-text-color-white" data-fancybox>Document</a> <span class="float-right passport-scan-text-color-white cursor-pointer"><a onclick="deleteSecondaryUploadedDoc(${res.ind})"><i class="fa fa-trash"></i></a></span>`).addClass('bg-success');
                   
                       // $('.seconday-details-' + res.ind).html(`<a href="${baseUrl + 'uploads/' + res.fileName}" class="passport-scan-text-color-white" data-fancybox>Document</a> <span class="float-right passport-scan-text-color-white cursor-pointer"><a onclick="deleteSecondaryDoc(${res.ind})"><i class="fa fa-trash"></i></a></span>`).addClass('bg-success');

                        // } else {
                        // $('.seconday-details-' + res.ind).text('Completed').removeClass('bg-danger').addClass('bg-success');
                    // }

                    // bind passport details
                    if (res.ocr.status === true) {
                        var row = res.ocr;
                        //append the additonal image
                        $('.seconday-details-' + res.ind).html(`<a href="${baseUrl + 'uploads/' + res.fileName}" class="passport-scan-text-color-white" data-fancybox>${row.documentType}</a> <span class="float-right passport-scan-text-color-white cursor-pointer"><a onclick="deleteSecondaryDoc(${res.ind})"><i class="fa fa-trash"></i></a></span>`);

                        switch (row.documentType) {
                            case 'passport':
                            break;
                                  case 'visa':
                                visa = true;
                                $('#visa_no').val(row.ocrData.visa_no);
                                $('#visa_date_of_issue').val(row.ocrData.date_of_issue);
                              var visaExpiry = row.ocrData.expiration_date ? moment(row.ocrData.expiration_date, "DD/MM/YYYY").format('DD/MM/YYYY') : '';
                               $('#visa_valid_till').val(visaExpiry);
                                if (visaExpiry) {
                                    var visaExpired = validateDateExpired(visaExpiry);
                                    validateDate(visaExpired, 'visa');
                                }
                              $('#visa_place_of_issue_country').val($('#visa_place_of_issue_country option[data-shortname="' + row.ocrData.country_code + '"]').val()).trigger('chosen:updated');
                              $('#type_of_visa').val($('#type_of_visa option[data-shortname="' + row.ocrData.type + '"]').val()).trigger('chosen:updated');
                                $('#visa_no_of_entry').val(row.ocrData.visa_entry);
                                var resVal = row.ocrData;
                                break;
                            default:
                                break;
                        }
                    } else {
                        // ocr error
                    }

                    // additional count
                    $('.additional-count').text($('.secondary-count').length);

                } else {

                    cuteAlert({
                        type: "success",
                        title: "Upload document",
                        message: res.msg,
                        buttonText: "Ok"
                    });
                }

            }, error: function (error, execution) {
                // $('.progress-width').addClass('d-none');
                var msg = getErrorMessage(error, execution);
                cuteAlert({
                    type: "success",
                    title: "Upload document",
                    message: msg,
                    buttonText: "Ok"
                })
            }, complete: function () {
            }
        });
    }
    function uploadPrimaryDoc(blob) {
        // send to api here
        var imgCount = imageInx;
        primaryDocUpdate=true;
        var formData = new FormData();
        formData.append('scan-document', blob);
        formData.append('ind', imgCount);
        formData.append('blob', 'yes');
        formData.append('type', 'passport');
        console.log("######AP URL",apiUrl)
        $.ajax({
            url: `${apiUrl}document-details/upload/document`,
            // url: `http://192.168.0.122/v1/api/document-details/upload/document`,
            method: 'POST',
            xhr: function () {
                $('.primary-doc-progress').removeClass('bg-danger').removeClass('bg-success');
                var myXhr = $.ajaxSettings.xhr();
                if (myXhr.upload) {
                    myXhr.upload.addEventListener('progress', function (e) {
                        if (e.lengthComputable) {
                            var max = e.total;
                            var current = e.loaded;
                            var percentage = ((current * 100) / max).toFixed(2);
                            if (percentage > 10) {
                                $('.primary-doc-progress').width(`${percentage}%`).text(percentage + ' %').css({ color: '#fff' });
                            } else {
                                $('.primary-doc-progress').width(`${percentage}%`).text(percentage + ' %').css({ color: '#155adc' });
                            }
                            if (percentage >= 100) {
                                $('.primary-doc-progress').text('Processing request...');
                            }
                        }
                    }, false);
                }
                return myXhr;
            },
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            beforeSend: function (xhr) {
                
                xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
            }, success: function (res) {
                if (res.status) {
                    attachments.primary = { fileName: res.fileName, thumbnail: res.thumbnail, ext: res.ext, ind: res.ind, thumb: res.genThumbnail, ocrData: res.ocr, isPrimary: true };

                    $('.primary-upload-preview-img img').attr('src', baseUrl + 'uploads/' + res.fileName);
                    $('.primary-upload-preview-img a').attr('href', baseUrl + 'uploads/' + res.fileName);

                    if (res.ocr.status === false) {
                        $('.primary-doc-progress').text('Please capture clear image').removeClass('bg-success').addClass('bg-danger');
                    } else {
                        $('.primary-doc-progress').text('Completed').removeClass('bg-danger').addClass('bg-success');
                    }

                    // bind passport details
                    if (res.ocr.status === true) {
                        var row = res.ocr;
                        console.log(row.ocrData);
                        console.log("################DOC",row.documentType)
                        
                        switch (row.documentType) {
                            
                            case 'P':
            case 'V':
            case 'DL':
            case 'VI':
            case 'O':
            case 'AC':  
            case 'PC':
                                passport = true;
                                $('#given_name').val(row.ocrData.first_name);
                                $('#family_name').val(row.ocrData.family_name);
                                $('#gender').val(row.ocrData.gender).trigger('chosen:updated');
                                $('#nationality').val($('#passport_place_of_issue_country option[data-shortname="' + row.ocrData.country_code + '"]').val()).trigger('chosen:updated');
                                $('#passport_place_of_issue_country').val($('#passport_place_of_issue_country option[data-shortname="' + row.ocrData.country_code + '"]').val()).trigger('chosen:updated');
                                $('#dob').val(row.ocrData.dob ? moment(row.ocrData.dob, "DD/MM/YYYY").format('DD/MM/YYYY') : '');
                                $('#passport_number').val(row.ocrData.passport_number);
                                $('#passport_place_of_issue').val(row.ocrData.passport_place_of_issue);
                                $('#passport_date_of_issue').val(row.ocrData.passport_issue_date);
                                $('#address').val(row.ocrData.place_of_birth);
                      
                var documentTypeName;
                switch (row.documentType) {
                    case 'P': documentTypeName = 'Passport'; break;
                    case 'AC': documentTypeName = 'Aadhar'; break;
                    case 'DL': documentTypeName = 'Driving license'; break;
                    case 'VI': documentTypeName = 'Voter ID'; break;
                    case 'V': documentTypeName = 'Visa'; break;
                    case 'O': documentTypeName = 'Others'; break;
                    case 'PC': documentTypeName = 'Pan Card'; break;
                    default: documentTypeName = ''; break;
                }
 
                $('#document_type option').each(function() {
                    if ($(this).text() === documentTypeName) {
                        var optionId = $(this).val();
                        console.log("########optionId", optionId);
                        $('#document_type').val(optionId).trigger('chosen:updated');
                        return false;
                    }
                });
 

                                

                                

                                
                                var resVal = row.ocrData;
                              
                              // var emptyGender = validateDateExpired($('#gender').val(row.ocrData.gender));
                               validateEmptyValues(resVal, row.documentType);
                                var passportExpiry = row.ocrData.passport_expiry ? moment(row.ocrData.passport_expiry, "DD/MM/YYYY").format('DD/MM/YYYY') : '';
                                $('#passport_valid_till').val(passportExpiry);

                                if (passportExpiry && resVal.first_name !=="" && resVal.gender !== "" && resVal.nationality !=="" && resVal.passport_place_of_issue_country !=="" && resVal.dob !=="" && resVal.passport_number !=="" && resVal.passport_place_of_issue !=="" && resVal.passport_date_of_issue !=="" && resVal.passport_expiry !=="") {
                                    
                                    var passportExpired = validateDateExpired(passportExpiry);
                                    validateDate(passportExpired, 'passport');
                                }
                                //  var img = row.ocrData.img ? row.ocrData.img : '';
                                //  if (img !== "" || img !== 'Non') {
                                //      // Convert to blob
                                //      profilePhoto = b64toBlob(img, 'data:image/jpg');
                                    
                                //      img ? $('.face-box img').attr('src', `data:image/jpg;base64,${img}`) : null;
                                //  } else {
                                     $('#nav-face-tab').trigger('click');
                                //  }
                                var img = row.ocrData.img ? row.ocrData.img : '';
                                console.log("################IMG RESP",img);
                                
                                if (img !== "" || img !== 'Non') {
                                    // Convert to blob
                                    profilePhoto = b64toBlob(img, 'data:image/jpeg');
                                    img ? $('.face-box img').attr('src', `data:image/jpeg;base64,${img}`) : null;

                                    // Manually change the file extension to .JPG
    const dataUrl = `data:image/jpeg;base64,${img}`;
    const dataUrlWithUpperCaseExtension = dataUrl.replace(/\.jpg/g, '.JPG');
    $('.face-box img').attr('src', dataUrlWithUpperCaseExtension);
                                } else {
                                   //$('#nav-face-tab').trigger('click');
                                }

                                break;
                                case 'visa':
                                //visa = true;
                                $('#visa_no').val(row.ocrData.visa_no);
                              
                              
                                var resVal = row.ocrData;
                              
                                break;
                            default:
                                break;
                        }
                    } else {
                        // ocr error
                    }

                } else {

                    cuteAlert({
                        type: "success",
                        title: "Upload document",
                        message: res.msg,
                        buttonText: "Ok"
                    });
                    
                }

            }, error: function (error, execution) {
                // $('.progress-width').addClass('d-none');
                var msg = getErrorMessage(error, execution);
                cuteAlert({
                    type: "success",
                    title: "Upload document",
                    message: msg,
                    buttonText: "Ok"
                });
            }, complete: function () {
            }
        });
    }

</script>
