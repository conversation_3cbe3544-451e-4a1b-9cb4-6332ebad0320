{"version": 3, "file": "popper.js.map", "sources": ["../src/popper/utils/isNative.js", "../src/popper/utils/debounce.js", "../src/popper/utils/isNumeric.js", "../src/popper/utils/setStyles.js", "../src/popper/utils/getSupportedPropertyName.js", "../src/popper/utils/isOffsetContainer.js", "../src/popper/utils/getRoot.js", "../src/popper/utils/getOffsetParent.js", "../src/popper/utils/findCommonOffsetParent.js", "../src/popper/utils/getStyleComputedProperty.js", "../src/popper/utils/getScroll.js", "../src/popper/utils/includeScroll.js", "../src/popper/utils/getParentNode.js", "../src/popper/utils/getScrollParent.js", "../src/popper/utils/getBordersSize.js", "../src/popper/utils/getWindowSizes.js", "../src/popper/utils/getClientRect.js", "../src/popper/utils/isIE10.js", "../src/popper/utils/getBoundingClientRect.js", "../src/popper/utils/getOffsetRectRelativeToArbitraryNode.js", "../src/popper/utils/getReferenceOffsets.js", "../src/popper/utils/getOuterSizes.js", "../src/popper/utils/getOppositePlacement.js", "../src/popper/utils/getPopperOffsets.js", "../src/popper/utils/isFunction.js", "../src/popper/utils/setupEventListeners.js", "../src/popper/utils/removeEventListeners.js", "../src/popper/utils/find.js", "../src/popper/utils/findIndex.js", "../src/popper/utils/runModifiers.js", "../src/popper/utils/isModifierEnabled.js", "../src/popper/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../src/popper/utils/isFixed.js", "../src/popper/utils/getBoundaries.js", "../src/popper/utils/computeAutoPlacement.js", "../src/popper/utils/placements.js", "../src/popper/utils/setAttributes.js", "../src/popper/modifiers/applyStyle.js", "../src/popper/utils/isModifierRequired.js", "../src/popper/modifiers/arrow.js", "../src/popper/utils/getOppositeVariation.js", "../src/popper/utils/clockwise.js", "../src/popper/modifiers/flip.js", "../src/popper/modifiers/keepTogether.js", "../src/popper/modifiers/offset.js", "../src/popper/modifiers/preventOverflow.js", "../src/popper/modifiers/shift.js", "../src/popper/modifiers/hide.js", "../src/popper/modifiers/inner.js", "../src/popper/modifiers/index.js", "../src/popper/index.js"], "sourcesContent": ["const nativeHints = [\n  'native code',\n  '[object MutationObserverConstructor]', // for mobile safari iOS 9.0\n];\n\n/**\n * Determine if a function is implemented natively (as opposed to a polyfill).\n * @argument {Function | undefined} fn the function to check\n * @returns {boolean}\n */\nexport default fn =>\n  nativeHints.some(hint => (fn || '').toString().indexOf(hint) > -1);\n", "import isNative from './isNative';\n\nconst isBrowser = typeof window !== 'undefined';\nconst longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nlet timeoutDuration = 0;\nfor (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nexport function microtaskDebounce(fn) {\n  let scheduled = false;\n  let i = 0;\n  const elem = document.createElement('span');\n\n  // MutationObserver provides a mechanism for scheduling microtasks, which\n  // are scheduled *before* the next task. This gives us a way to debounce\n  // a function but ensure it's called *before* the next paint.\n  const observer = new MutationObserver(() => {\n    fn();\n    scheduled = false;\n  });\n\n  observer.observe(elem, { attributes: true });\n\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      elem.setAttribute('x-index', i);\n      i = i + 1; // don't use compund (+=) because it doesn't get optimized in V8\n    }\n  };\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\n// It's common for MutationObserver polyfills to be seen in the wild, however\n// these rely on Mutation Events which only occur when an element is connected\n// to the DOM. The algorithm used in this module does not use a connected element,\n// and so we must ensure that a *native* MutationObserver is available.\nconst supportsNativeMutationObserver =\n  isBrowser && isNative(window.MutationObserver);\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsNativeMutationObserver\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles - Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 && isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'webkit', 'moz', 'o'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length - 1; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof window.document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "export default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || element.firstElementChild.offsetParent === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  const offsetParent = element && element.offsetParent;\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return window.document.documentElement;\n  }\n\n  return offsetParent;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return window.document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    element1 !== commonAncestorContainer && element2 !== commonAncestorContainer\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {Number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = window.document.documentElement;\n    const scrollingElement = window.document.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (\n    !element || ['HTML', 'BODY', '#document'].indexOf(element.nodeName) !== -1\n  ) {\n    return window.document.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles - result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {Number} borders - the borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    +styles[`border${sideA}Width`].split('px')[0] +\n    +styles[`border${sideB}Width`].split('px')[0]\n  );\n}\n", "export default function getWindowSizes() {\n  const body = window.document.body;\n  const html = window.document.documentElement;\n  return {\n    height: Math.max(\n      body.scrollHeight,\n      body.offsetHeight,\n      html.clientHeight,\n      html.scrollHeight,\n      html.offsetHeight\n    ),\n    width: Math.max(\n      body.scrollWidth,\n      body.offsetWidth,\n      html.clientWidth,\n      html.scrollWidth,\n      html.offsetWidth\n    ),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "/**\n * Tells if you are running Internet Explorer 10\n * @returns {Boolean} isIE10\n */\nexport default function() {\n  return navigator.appVersion.indexOf('MSIE 10') !== -1;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport runIsIE10 from './isIE10';\nconst isIE10 = runIsIE10();\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10) {\n    try {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  const width =\n    sizes.width || element.clientWidth || result.right - result.left;\n  const height =\n    sizes.height || element.clientHeight || result.bottom - result.top;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE10 from './isIE10';\nimport getClientRect from './getClientRect';\nconst isIE10 = runIsIE10();\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent) {\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top,\n    left: childrenRect.left - parentRect.left,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (isHTML || parent.nodeName === 'BODY') {\n    const styles = getStyleComputedProperty(parent);\n    const borderTopWidth = isIE10 && isHTML\n      ? 0\n      : +styles.borderTopWidth.split('px')[0];\n    const borderLeftWidth = isIE10 && isHTML\n      ? 0\n      : +styles.borderLeftWidth.split('px')[0];\n    const marginTop = isIE10 && isHTML ? 0 : +styles.marginTop.split('px')[0];\n    const marginLeft = isIE10 && isHTML ? 0 : +styles.marginLeft.split('px')[0];\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    parent.contains(scrollParent) &&\n    (isIE10 || scrollParent.nodeName !== 'BODY')\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode\n  from './getOffsetRectRelativeToArbitraryNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference) {\n  const commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  const y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one/\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(\n  position,\n  popper,\n  referenceOffsets,\n  placement\n) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    position,\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {*} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "import getScrollParent from './getScrollParent';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? window : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  window.addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  window.removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\n\n/**\n * Loop trough the list of modifiers and run them in order, each of them will then edit the data object\n * @method\n * @memberof Popper.Utils\n * @param {Object} data\n * @param {Array} modifiers\n * @param {Function} ends\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier.enabled && isFunction(modifier.function)) {\n      data = modifier.function(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "import getOffsetRectRelativeToArbitraryNode\n  from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  const html = window.document.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = getScroll(html);\n  const scrollLeft = getScroll(html, 'left');\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode\n  from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode\n  from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {Object} data - Object containing the property \"offsets\" generated by `_getOffsets`\n * @param {Number} padding - Boundaries padding\n * @param {Element} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement\n) {\n  // NOTE: 1 DOM access here\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(popper));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = window.document.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = window.document.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes();\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(popper, reference, 0, 'scrollParent');\n\n  const sides = {\n    top: refRect.top - boundaries.top,\n    right: boundaries.right - refRect.right,\n    bottom: boundaries.bottom - refRect.bottom,\n    left: refRect.left - boundaries.left,\n  };\n\n  const computedPlacement = Object.keys(sides).sort(\n    (a, b) => sides[b] - sides[a]\n  )[0];\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "const placements = [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n\nexport default placements;\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles - Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * Apply the computed styles to the popper element\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data, options) {\n  // apply the final offsets to the popper\n  // NOTE: 1 DOM access here\n  const styles = {\n    position: data.offsets.popper.position,\n  };\n\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // round top and left to avoid blurry text\n  const left = Math.round(data.offsets.popper.left);\n  const top = Math.round(data.offsets.popper.top);\n\n  // if gpuAcceleration is set to true and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n  if (options.gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles.top = 0;\n    styles.left = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `left` and `top` properties\n    styles.left = left;\n    styles.top = top;\n    styles.willChange = 'top, left';\n  }\n\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, { ...styles, ...data.styles });\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, { ...attributes, ...data.attributes });\n\n  // if the arrow style has been computed, apply the arrow style\n  if (data.offsets.arrow) {\n    setStyles(data.arrowElement, data.offsets.arrow);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used to add margins to the popper\n * margins needs to be calculated to get the correct popper offsets\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  options.placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference\n  );\n\n  popper.setAttribute('x-placement', options.placement);\n  return options;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  return (\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    })\n  );\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\n\n/**\n * Modifier used to move the arrowElements on the edge of the popper to make sure them are always between the popper and the reference element\n * It will use the CSS outer size of the arrowElement element to know how many pixels of conjuction are needed\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    console.warn(\n      'WARNING: `keepTogether` modifier is required by arrow modifier in order to work, be sure to include it before `arrow`!'\n    );\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const popper = getClientRect(data.offsets.popper);\n  const reference = data.offsets.reference;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const side = isVertical ? 'top' : 'left';\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  let sideValue = center - getClientRect(data.offsets.popper)[side];\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {};\n  data.offsets.arrow[side] = sideValue;\n  data.offsets.arrow[altSide] = ''; // make sure to unset any eventual altSide value from the DOM node\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one/\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "import placements from '../utils/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getClientRect from '../utils/getClientRect';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * Modifier used to flip the placement of the popper when the latter is starting overlapping its reference element.\n * Requires the `preventOverflow` modifier before it in order to work.\n * **NOTE:** data.instance modifier will run all its previous modifiers everytime it tries to flip the popper!\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = getClientRect(data.offsets.popper);\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n      data.offsets.popper = getPopperOffsets(\n        data.instance.state.position,\n        data.instance.popper,\n        data.offsets.reference,\n        data.placement\n      );\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\n\n/**\n * Modifier used to make sure the popper is always near its reference element\n * It cares only about the first axis, you can still have poppers with margin\n * between the popper and its reference element.\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const popper = getClientRect(data.offsets.popper);\n  const reference = data.offsets.reference;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Modifier used to add an offset to the popper, useful if you more granularity positioning your popper.\n * The offsets will shift the popper on the side of its reference element.\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n *      Basic usage allows a number used to nudge the popper by the given amount of pixels.\n *      You can pass a percentage value as string (eg. `20%`) to nudge by the given percentage (relative to reference element size)\n *      Other supported units are `vh` and `vw` (relative to viewport)\n *      Additionally, you can pass a pair of values (eg. `10 20` or `2vh 20%`) to nudge the popper\n *      on both axis.\n *      A note about percentage values, if you want to refer a percentage to the popper size instead of the reference element size,\n *      use `%p` instead of `%` (eg: `20%p`). To make it clearer, you can replace `%` with `%r` and use eg.`10%p 25%r`.\n *      > **Heads up!** The order of the axis is relative to the popper placement: `bottom` or `top` are `X,Y`, the other are `Y,X`\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, options) {\n  const placement = data.placement;\n  const popper = data.offsets.popper;\n\n  let offsets;\n  if (isNumeric(options.offset)) {\n    offsets = [options.offset, 0];\n  } else {\n    // split the offset in case we are providing a pair of offsets separated\n    // by a blank space\n    offsets = options.offset.split(' ');\n\n    // itherate through each offset to compute them in case they are percentages\n    offsets = offsets.map((offset, index) => {\n      // separate value from unit\n      const split = offset.match(/(\\d*\\.?\\d*)(.*)/);\n      const value = +split[1];\n      const unit = split[2];\n\n      // use height if placement is left or right and index is 0 otherwise use width\n      // in this way the first offset will use an axis and the second one\n      // will use the other one\n      let useHeight =\n        placement.indexOf('right') !== -1 || placement.indexOf('left') !== -1;\n\n      if (index === 1) {\n        useHeight = !useHeight;\n      }\n\n      const measurement = useHeight ? 'height' : 'width';\n\n      // if is a percentage relative to the popper (%p), we calculate the value of it using\n      // as base the sizes of the popper\n      // if is a percentage (% or %r), we calculate the value of it using as base the\n      // sizes of the reference element\n      if (unit.indexOf('%') === 0) {\n        let element;\n        switch (unit) {\n          case '%p':\n            element = data.offsets.popper;\n            break;\n          case '%':\n          case '$r':\n          default:\n            element = data.offsets.reference;\n        }\n\n        const rect = getClientRect(element);\n        const len = rect[measurement];\n        return len / 100 * value;\n      } else if (unit === 'vh' || unit === 'vw') {\n        // if is a vh or vw, we calculate the size based on the viewport\n        let size;\n        if (unit === 'vh') {\n          size = Math.max(\n            document.documentElement.clientHeight,\n            window.innerHeight || 0\n          );\n        } else {\n          size = Math.max(\n            document.documentElement.clientWidth,\n            window.innerWidth || 0\n          );\n        }\n        return size / 100 * value;\n      } else if (unit === 'px') {\n        // if is an explicit pixel unit, we get rid of the unit and keep the value\n        return +value;\n      } else {\n        // if is an implicit unit, it's px, and we return just the value\n        return +offset;\n      }\n    });\n  }\n\n  if (data.placement.indexOf('left') !== -1) {\n    popper.top += offsets[0];\n    popper.left -= offsets[1] || 0;\n  } else if (data.placement.indexOf('right') !== -1) {\n    popper.top += offsets[0];\n    popper.left += offsets[1] || 0;\n  } else if (data.placement.indexOf('top') !== -1) {\n    popper.left += offsets[0];\n    popper.top -= offsets[1] || 0;\n  } else if (data.placement.indexOf('bottom') !== -1) {\n    popper.left += offsets[0];\n    popper.top += offsets[1] || 0;\n  }\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\n\n/**\n * Modifier used to prevent the popper from being positioned outside the boundary.\n *\n * An scenario exists where the reference itself is not within the boundaries. We can\n * say it has \"escaped the boundaries\" — or just \"escaped\". In this case we need to\n * decide whether the popper should either:\n *\n * - detach from the reference and remain \"trapped\" in the boundaries, or\n * - if it should be ignore the boundary and \"escape with the reference\"\n *\n * When `escapeWithReference` is `true`, and reference is completely outside the\n * boundaries, the popper will overflow (or completely leave) the boundaries in order\n * to remain attached to the edge of the reference.\n *\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  const boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement\n  );\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = getClientRect(data.offsets.popper);\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side = ['left', 'top'].indexOf(placement) !== -1\n      ? 'primary'\n      : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\n\n/**\n * Modifier used to shift the popper on the start or end of its reference element side\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const reference = data.offsets.reference;\n    const popper = getClientRect(data.offsets.popper);\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * Modifier used to hide the popper when its reference element is outside of the\n * popper boundaries. It will set an x-hidden attribute which can be used to hide\n * the popper when its reference is out of boundaries.\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    console.warn(\n      'WARNING: preventOverflow modifier is required by hide modifier in order to work, be sure to include it before hide!'\n    );\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * Modifier used to make the popper flow toward the inner of the reference element.\n * By default, when this modifier is disabled, the popper will be placed outside\n * the reference element.\n * @method\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const popper = getClientRect(data.offsets.popper);\n  const reference = getClientRect(data.offsets.reference);\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[placement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `function` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference element side\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {Number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: shift,\n  },\n\n  /**\n   * Modifier used to add an offset to the popper, useful if you more granularity positioning your popper.\n   * The offsets will shift the popper on the side of its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {Number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: offset,\n    /** @prop {Number|String} offset=0\n     * Basic usage allows a number used to nudge the popper by the given amount of pixels.\n     * You can pass a percentage value as string (eg. `20%`) to nudge by the given percentage (relative to reference element size)\n     * Other supported units are `vh` and `vw` (relative to viewport)\n     * Additionally, you can pass a pair of values (eg. `10 20` or `2vh 20%`) to nudge the popper\n     * on both axis.\n     * A note about percentage values, if you want to refer a percentage to the popper size instead of the reference element size,\n     * use `%p` instead of `%` (eg: `20%p`). To make it clearer, you can replace `%` with `%r` and use eg.`10%p 25%r`.\n     * **Heads up!** The order of the axis is relative to the popper placement: `bottom` or `top` are `X,Y`, the other are `Y,X`\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries. We can\n   * say it has \"escaped the boundaries\" — or just \"escaped\". In this case we need to\n   * decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should be ignore the boundary and \"escape with the reference\"\n   *\n   * When `escapeWithReference` is `true`, and reference is completely outside the\n   * boundaries, the popper will overflow (or completely leave) the boundaries in order\n   * to remain attached to the edge of the reference.\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {Number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: preventOverflow,\n    /**\n     * @prop {Array} priority=['left', 'right', 'top', 'bottom']\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {Number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {Number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: keepTogether,\n  },\n\n  /**\n   * Modifier used to move the `arrowElement` on the edge of the popper to make\n   * sure it's always between the popper and its reference element.\n   * It will use the CSS outer size of the `arrowElement` element to know how\n   * many pixels of conjuction are needed.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {Number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the placement of the popper when the latter starts\n   * overlapping its reference element.\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * **NOTE:** this modifier will run all its previous modifiers everytime it\n   * tries to flip the popper!\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {Number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {Number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport',\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {Number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {Function} */\n    function: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set an x-hidden attribute which can be used to hide\n   * the popper when its reference is out of boundaries.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {Number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: hide,\n  },\n\n  /**\n   * Applies the computed styles to the popper element, disabling this modifier,\n   * no DOM changes will be performed by Popper.js. You may want to disble it\n   * while working with view librararies like React.\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {Number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {Function} */\n    function: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n  },\n};\n\n/**\n * Modifiers can edit the `data` object to change the beheavior of the popper.\n * This object contains all the informations used by Popper.js to compute the\n * popper position.\n * The modifier can edit the data as needed, and then `return` it as result.\n *\n * @callback Modifiers~modifier\n * @param {dataObject} data\n * @return {dataObject} modified data\n */\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arro] `top` and `left` offsets, only one of them will be different from 0\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport setStyles from './utils/setStyles';\nimport getSupportedPropertyName from './utils/getSupportedPropertyName';\nimport getReferenceOffsets from './utils/getReferenceOffsets';\nimport getPopperOffsets from './utils/getPopperOffsets';\nimport isFunction from './utils/isFunction';\nimport setupEventListeners from './utils/setupEventListeners';\nimport removeEventListeners from './utils/removeEventListeners';\nimport runModifiers from './utils/runModifiers';\nimport isModifierEnabled from './utils/isModifierEnabled';\nimport computeAutoPlacement from './utils/computeAutoPlacement';\nimport placements from './utils/placements';\n\n// Modifiers\nimport modifiers from './modifiers/index';\n\n/**\n *\n * @callback onCreateCallback\n * @param {dataObject} data\n */\n\n/**\n *\n * @callback onUpdateCallback\n * @param {dataObject} data\n */\n\n/**\n * Default options provided to Popper.js constructor.\n * These can be overriden using the `options` argument of Popper.js.\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of {defaults}, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @namespace defaults\n */\nconst DEFAULTS = {\n  /**\n   * Popper's placement\n   * @memberof defaults\n   * @prop {String} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @memberof defaults\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @memberof defaults\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.\n   * By default, is set to no-op.\n   * Access Popper.js instance with `data.instance`.\n   * @memberof defaults\n   * @prop {onCreateCallback}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.\n   * By default, is set to no-op.\n   * Access Popper.js instance with `data.instance`.\n   * @memberof defaults\n   * @prop {onUpdateCallback}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @memberof defaults\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * Create a new Popper.js instance\n * @class Popper\n * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Your custom options to override the ones defined in [DEFAULTS](#defaults)\n * @return {Object} instance - The generated Popper.js instance\n */\nexport default class Popper {\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference.jquery ? reference[0] : reference;\n    this.popper = popper.jquery ? popper[0] : popper;\n\n    // make sure to apply the popper position before any computation\n    setStyles(this.popper, { position: 'absolute' });\n\n    // refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(Popper.Defaults.modifiers).map(name => ({\n      name,\n      ...Popper.Defaults.modifiers[name],\n    }));\n\n    // assign default values to modifiers, making sure to override them with\n    // the ones defined by user\n    this.modifiers = this.modifiers.map(defaultConfig => {\n      const userConfig = (options.modifiers &&\n        options.modifiers[defaultConfig.name]) || {};\n      return { ...defaultConfig, ...userConfig };\n    });\n\n    // add custom modifiers to the modifiers list\n    if (options.modifiers) {\n      this.options.modifiers = {\n        ...{},\n        ...Popper.Defaults.modifiers,\n        ...options.modifiers,\n      };\n      Object.keys(options.modifiers).forEach(name => {\n        // take in account only custom modifiers\n        if (Popper.Defaults.modifiers[name] === undefined) {\n          const modifier = options.modifiers[name];\n          modifier.name = name;\n          this.modifiers.push(modifier);\n        }\n      });\n    }\n\n    // sort the modifiers by order\n    this.modifiers = this.modifiers.sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  //\n  // Methods\n  //\n\n  /**\n     * Updates the position of the popper, computing the new offsets and applying the new style\n     * Prefer `scheduleUpdate` over `update` because of performance reasons\n     * @method\n     * @memberof Popper\n     */\n  update() {\n    // if popper is destroyed, don't perform any further update\n    if (this.state.isDestroyed) {\n      return;\n    }\n\n    let data = {\n      instance: this,\n      styles: {},\n      attributes: {},\n      flipped: false,\n      offsets: {},\n    };\n\n    // compute reference element offsets\n    data.offsets.reference = getReferenceOffsets(\n      this.state,\n      this.popper,\n      this.reference\n    );\n\n    // compute auto placement, store placement inside the data object,\n    // modifiers will be able to edit `placement` if needed\n    // and refer to originalPlacement to know the original value\n    data.placement = computeAutoPlacement(\n      this.options.placement,\n      data.offsets.reference,\n      this.popper,\n      this.reference\n    );\n\n    // store the computed placement inside `originalPlacement`\n    data.originalPlacement = this.options.placement;\n\n    // compute the popper offsets\n    data.offsets.popper = getPopperOffsets(\n      this.state,\n      this.popper,\n      data.offsets.reference,\n      data.placement\n    );\n\n    // run the modifiers\n    data = runModifiers(this.modifiers, data);\n\n    // the first `update` will call `onCreate` callback\n    // the other ones will call `onUpdate` callback\n    if (!this.state.isCreated) {\n      this.state.isCreated = true;\n      this.options.onCreate(data);\n    } else {\n      this.options.onUpdate(data);\n    }\n  }\n\n  /**\n     * Schedule an update, it will run on the next UI update available\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n     * Destroy the popper\n     * @method\n     * @memberof Popper\n     */\n  destroy() {\n    this.state.isDestroyed = true;\n\n    // touch DOM only if `applyStyle` modifier is enabled\n    if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n      this.popper.removeAttribute('x-placement');\n      this.popper.style.left = '';\n      this.popper.style.position = '';\n      this.popper.style.top = '';\n      this.popper.style[getSupportedPropertyName('transform')] = '';\n    }\n\n    this.disableEventListeners();\n\n    // remove the popper if user explicity asked for the deletion on destroy\n    // do not use `remove` because IE11 doesn't support it\n    if (this.options.removeOnDestroy) {\n      this.popper.parentNode.removeChild(this.popper);\n    }\n    return this;\n  }\n\n  /**\n     * it will add resize/scroll events and start recalculating\n     * position of the popper element when they are triggered\n     * @method\n     * @memberof Popper\n     */\n  enableEventListeners() {\n    if (!this.state.eventsEnabled) {\n      this.state = setupEventListeners(\n        this.reference,\n        this.options,\n        this.state,\n        this.scheduleUpdate\n      );\n    }\n  }\n\n  /**\n     * it will remove resize/scroll events and won't recalculate\n     * popper position when they are triggered. It also won't trigger onUpdate callback anymore,\n     * unless you call 'update' method manually.\n     * @method\n     * @memberof Popper\n     */\n  disableEventListeners() {\n    if (this.state.eventsEnabled) {\n      window.cancelAnimationFrame(this.scheduleUpdate);\n      this.state = removeEventListeners(this.reference, this.state);\n    }\n  }\n\n  /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     * @memberof Popper\n     */\n  static Utils = window.PopperUtils;\n\n  /**\n     * List of accepted placements to use as values of the `placement` option\n     * @memberof Popper\n     */\n  static placements = placements;\n\n  /**\n     * Default Popper.js options\n     * @memberof Popper\n     */\n  static Defaults = DEFAULTS;\n}\n\n/**\n  * The `referenceObject` is an object that provides an interface compatible with Popper.js\n  * and lets you use it as replacement of a real DOM node.\n  * You can use this method to position a popper relatively to a set of coordinates\n  * in case you don't have a DOM node to use as reference.\n  * NB: This feature isn't supported in Internet Explorer 10\n  * @name referenceObject\n  * @property {Function} data.getBoundingClientRect A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n  * @property {Number} data.clientWidth An ES6 getter that will return the width of the virtual reference element.\n  * @property {Number} data.clientHeight An ES6 getter that will return the height of the virtual reference element.\n  */\n"], "names": ["nativeHints", "fn", "some", "hint", "toString", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "window", "longerTimeoutBrowsers", "timeoutDuration", "i", "length", "navigator", "userAgent", "microtaskDebounce", "scheduled", "elem", "document", "createElement", "observer", "MutationObserver", "observe", "attributes", "setAttribute", "taskDebounce", "supportsNativeMutationObserver", "isNative", "isNumeric", "n", "isNaN", "parseFloat", "isFinite", "setStyles", "element", "styles", "keys", "for<PERSON>ach", "prop", "unit", "style", "getSupportedPropertyName", "property", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "slice", "prefix", "to<PERSON><PERSON><PERSON>", "body", "isOffsetContainer", "nodeName", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetParent", "getRoot", "node", "parentNode", "getOffsetParent", "documentElement", "findCommonOffsetParent", "element1", "element2", "nodeType", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "element1root", "host", "getStyleComputedProperty", "css", "getComputedStyle", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getParentNode", "getScrollParent", "overflow", "overflowX", "overflowY", "test", "getBordersSize", "axis", "sideA", "sideB", "split", "getWindowSizes", "Math", "max", "scrollHeight", "offsetHeight", "clientHeight", "scrollWidth", "offsetWidth", "clientWidth", "getClientRect", "offsets", "width", "height", "appVersion", "isIE10", "runIsIE10", "getBoundingClientRect", "err", "result", "sizes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vertScrollbar", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "contains", "getReferenceOffsets", "state", "popper", "reference", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "placement", "hash", "replace", "matched", "getPopperOffsets", "position", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "isFunction", "functionToCheck", "getType", "call", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "options", "updateBound", "scrollElement", "eventsEnabled", "removeEventListeners", "removeEventListener", "find", "arr", "check", "Array", "prototype", "filter", "findIndex", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "undefined", "enabled", "function", "isModifierEnabled", "modifierName", "name", "getViewportOffsetRectRelativeToArtbitraryNode", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getBoundaries", "padding", "boundariesElement", "boundaries", "boundariesNode", "computeAutoPlacement", "refRect", "sides", "computedPlacement", "Object", "sort", "a", "b", "variation", "placements", "setAttributes", "removeAttribute", "applyStyle", "round", "prefixedProperty", "gpuAcceleration", "<PERSON><PERSON><PERSON><PERSON>", "instance", "arrow", "arrowElement", "applyStyleOnLoad", "modifierOptions", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "warn", "querySelector", "isVertical", "len", "altSide", "opSide", "arrowElementSize", "center", "sideValue", "min", "getOppositeVariation", "validPlacements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flip", "flipped", "originalPlacement", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "step", "refOffsets", "floor", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariation", "flipVariations", "keepTogether", "map", "useHeight", "size", "preventOverflow", "priority", "escapeWithReference", "shift", "basePlacement", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "DEFAULTS", "<PERSON><PERSON>", "scheduleUpdate", "requestAnimationFrame", "update", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "defaultConfig", "userConfig", "onLoad", "enableEventListeners", "isDestroyed", "isCreated", "onCreate", "onUpdate", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "cancelAnimationFrame", "Utils", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "e0C2EkB,KAAd,AAAmB,eHCA,KAAA,AAAK,eFhDb,KAAA,AAAK,etBxBR,KAAA,AAAK,IfJjB,KAAA,AAAM,AAAc,AAClB,AACA,oEAQF,aAAe,KACb,YAAA,AAAY,KAAK,KAA8C,CADjE,AACE,AAAgE,CAAvC,EAAC,GAAD,AAAO,IAAP,AAAW,WAAX,AAAsB,QAAtB,AAA8B,ICTzD,KAAM,WAAN,AAAoC,AACpC,WADkB,QAAA,AAAO,QACzB,AAAM,AAAwB,AAAC,AAAQ,AAAW,mDAClD,GAAI,iBAAJ,AAAsB,EACtB,IAAK,GAAI,GAAT,AAAa,EAAG,EAAI,sBAApB,AAA0C,OAAQ,GAAlD,AAAuD,AAAG,KACpD,WAAJ,AAA0E,CAAzD,YAAA,AAAU,UAAV,AAAoB,QAAQ,sBAA5B,AAA4B,AAAsB,IAAU,iBAC3E,AAAkB,QAKtB,AAAO,QAAA,AAAS,AAAkB,qBAAI,IACpC,AAAI,AAAY,MACZ,EAAJ,AAAQ,OACF,GAAO,SAAA,AAAS,cAAtB,AAAa,AAAuB,QAK9B,EAAW,GAAA,AAAI,kBAAiB,IAAM,KAE1C,AAAY,IAFd,AAAiB,YAKjB,AAAS,QAAT,AAAiB,EAAM,CAAvB,AAAuB,AAAE,AAAY,gBAE9B,IAAM,AACP,CAAJ,AAAK,AAAW,IACd,AAAY,OACZ,AAAK,aAAL,AAAkB,UAFJ,AAEd,AAA6B,AAC7B,AAAI,AAAI,OAJZ,EASF,AAAO,QAAA,AAAS,AAAa,gBAAI,IAC/B,AAAI,AAAY,YACT,IAAM,AACP,CAAJ,AAAK,AAAW,IACd,AAAY,gBACD,IAAM,CACf,AAAY,QADd,EAAA,AAGG,iBANP,EAeF,KAAM,gCACJ,WAAa,SAAS,OADxB,AACe,AAAgB,kBAW/B,aAAgB,+BAAA,AACZ,kBADJ,AAEI,ACnEJ,aAOA,AAAe,QAAA,AAAS,AAAU,aAAG,OAC5B,AAAM,EAAN,MAAY,CAAC,MAAM,WAAnB,AAAa,AAAM,AAAW,KAAO,SAA5C,AAA4C,AAAS,ECNvD,CAOA,AAAe,QAAA,AAAS,AAAU,AAAS,eAAQ,QACjD,AAAO,KAAP,AAAY,GAAZ,AAAoB,QAAQ,KAAQ,IAC9B,GAAJ,AAAW,GAIP,CADF,AACG,CADH,AAAC,AAAS,AAAU,AAAO,AAAS,AAAU,oDAA9C,AAAsD,QAAtD,AAA8D,IACtD,UAAU,EAFpB,AAEU,AAAU,AAAO,AACzB,QACA,AAAO,QAET,AAAQ,MAAR,AAAc,GAAQ,EAAA,AAAO,GAA7B,AAAqC,CATvC,ECVF,CAOA,AAAe,QAAA,AAAS,AAAyB,4BAAU,MACzD,AAAM,AAAW,AAAC,AAAO,AAAM,AAAU,AAAO,gCAC1C,EAAY,EAAA,AAAS,OAAT,AAAgB,GAAhB,AAAmB,cAAgB,EAAA,AAAS,MAA9D,AAAqD,AAAe,OAE/D,GAAI,GAAT,AAAa,EAAG,EAAI,EAAA,AAAS,OAA7B,AAAsC,EAAtC,AAAyC,IAAK,MACtC,GAAS,EAAf,AAAe,AAAS,GAClB,EAAU,AAAU,KAAE,AAAO,IAAE,AAAU,CAA/B,GAAhB,AAAmD,KACnD,AAAmD,AAAa,WAA5D,QAAO,QAAA,AAAO,SAAP,AAAgB,KAAhB,AAAqB,MAA5B,AAAO,AAA2B,SACpC,AAAO,SAGX,AAAO,MClBM,QAAA,AAAS,AAAkB,qBAAS,MAC3C,CAAA,AAAE,YAAR,AAAqB,QACrB,AAAiB,AAAQ,AACvB,AAAO,MADL,OAIF,AAAa,MAAb,MAAuB,EAAA,AAAQ,kBAAR,AAA0B,eADnD,AACoE,ECNtE,CAOA,AAAe,QAAA,AAAS,AAAQ,WAAM,OACpC,AAAwB,AAAM,KAA1B,KAAA,AAAK,WAIT,AAAO,EAHE,QAAQ,EAAf,AAAO,AAAa,WCTxB,CAOA,AAAe,QAAA,AAAS,AAAgB,mBAAS,MAEzC,GAAe,GAAW,EAAhC,AAAwC,aAClC,EAAW,GAAgB,EAAjC,AAA8C,eAE1C,AAAC,EAAD,EAAA,AAA0B,MAAb,MAAjB,AAAqD,AAAQ,MAArB,KAIxC,AAAO,EAHE,OAAA,AAAO,SAAd,AAAuB,eCT3B,CAQA,AAAe,QAAA,AAAS,AAAuB,AAAU,4BAAU,IAE7D,CAAA,AAAC,GAAY,CAAC,EAAd,AAAuB,UAAY,CAAnC,AAAoC,GAAY,CAAC,EAArD,AAA8D,AAAU,eAC/D,QAAA,AAAO,SAAd,AAAuB,qBAInB,GACJ,EAAA,AAAS,wBAAT,AAAiC,GACjC,KAFF,AAEO,4BACD,EAAQ,EAAA,AAAQ,EAAtB,AAAiC,EAC3B,EAAM,EAAA,AAAQ,EAApB,AAA+B,EAGzB,EAAQ,SAAd,AAAc,AAAS,gBACvB,AAAM,SAAN,AAAe,EAAf,AAAsB,KACtB,AAAM,OAAN,AAAa,EAAb,AAAkB,QACZ,CAAA,AAAE,2BAAR,AAAoC,KAIlC,IAAA,AAAa,GAA2B,IAD1C,AACuD,AACrD,QACI,mBAAJ,AAAI,AAAkB,AAA0B,GAC9C,AAAO,EAGF,gBAAP,AAAO,AAAgB,QAInB,GAAe,QAArB,AAAqB,AAAQ,SACzB,GAAJ,AAAiB,AAAM,KACd,uBAAuB,EAAvB,AAAoC,KAD7C,AACE,AAAO,AAA0C,AAC5C,GACE,uBAAA,AAAuB,EAAU,QAAA,AAAQ,GAAhD,AAAO,AAAmD,KC/C9D,CAOA,AAAe,QAAA,AAAS,AAAyB,AAAS,8BAAU,IAClE,AAAyB,AAAG,CAAxB,KAAA,AAAQ,SACV,AAAO,cAGH,GAAM,OAAA,AAAO,iBAAP,AAAwB,EAApC,AAAY,AAAiC,YACtC,GAAW,EAAX,AAAW,AAAI,GAAtB,AAAkC,CCbpC,CAQA,AAAe,QAAA,AAAS,AAAU,aAAS,EAA5B,AAAmC,MAAO,MACjD,GAAY,AAAS,KAAT,KAAA,AAAiB,YAAnC,AAAiD,aAC3C,EAAW,EAAjB,AAAyB,YAErB,AAAa,MAAb,MAAJ,AAAwC,MAAb,KAAqB,MACxC,GAAO,OAAA,AAAO,SAApB,AAA6B,gBACvB,EAAmB,OAAA,AAAO,SAAP,AAAgB,kBAAzC,AAA6D,QACtD,GAAP,AAAO,AAAiB,SAGnB,GAAP,AAAO,AAAQ,EChBjB,CASA,AAAe,QAAA,AAAS,AAAc,AAAM,mBAA7B,AAAsC,AAAW,KAAO,MAC/D,GAAY,UAAA,AAAU,EAA5B,AAAkB,AAAmB,OAC/B,EAAa,UAAA,AAAU,EAA7B,AAAmB,AAAmB,QAChC,EAAW,EAAW,CAAX,AAAY,EAA7B,AAAiC,WACjC,AAAK,KAAO,EAAZ,AAAwB,IACxB,AAAK,QAAU,EAAf,AAA2B,IAC3B,AAAK,MAAQ,EAAb,AAA0B,IAC1B,AAAK,OAAS,EAAd,AAA2B,EAC3B,AAAO,CCnBT,CAOA,AAAe,QAAA,AAAS,AAAc,iBAAS,OAC7C,AAAyB,AAAQ,MAA7B,KAAA,AAAQ,SACV,AAAO,EAEF,EAAA,AAAQ,YAAc,EAA7B,AAAqC,ICRvC,CAOA,AAAe,QAAA,AAAS,AAAgB,mBAAS,IAG7C,CAAA,AAAC,GAAuE,CAD1E,AAC2E,AACzE,CADY,AAAC,AAAQ,AAAQ,+BAAjB,AAA8B,QAAQ,EAAtC,AAA8C,gBAEnD,QAAA,AAAO,SAAd,AAAuB,UAInB,CAAA,AAAE,WAAF,AAAY,YAAZ,AAAuB,aAAc,yBAA3C,AAA2C,AAAyB,SAChE,iBAAA,AAAgB,KAAK,EAAA,AAAW,EAApC,AAAI,AAA4C,AAAY,GAC1D,AAAO,EAGF,gBAAgB,cAAvB,AAAO,AAAgB,AAAc,GCxBvC,CASA,AAAe,QAAA,AAAS,AAAe,AAAQ,oBAAM,MAC7C,GAAQ,AAAS,GAAT,KAAA,AAAe,OAA7B,AAAsC,MAChC,EAAQ,AAAU,MAAV,IAAA,AAAmB,QAAjC,AAA2C,eAGzC,CAAC,AAAQ,WAAQ,AAAM,CAAtB,SAAA,AAA8B,MAA9B,AAAoC,MAArC,AAAC,AAA0C,GAC3C,EAAC,AAAQ,WAAQ,AAAM,CAAtB,SAAA,AAA8B,MAA9B,AAAoC,MAFvC,AAEG,AAA0C,GCfhC,QAAA,AAAS,iBAAiB,MACjC,GAAO,OAAA,AAAO,SAApB,AAA6B,KACvB,EAAO,OAAA,AAAO,SAApB,AAA6B,sBACtB,iBAEH,EADM,AACD,aACL,EAFM,AAED,aACL,EAHM,AAGD,aACL,EAJM,AAID,aACL,EANG,AACG,AAKD,oBAEA,AAAK,SACV,EADK,AACA,YACL,EAFK,AAEA,YACL,EAHK,AAGA,YACL,EAJK,AAIA,YACL,EAbJ,AAQS,AAKA,wLChBX,EAOA,AAAe,QAAA,AAAS,AAAc,iBAAS,oBAC7C,AACK,SACI,EAAA,AAAQ,KAAO,EAFxB,AAEgC,aACtB,EAAA,AAAQ,IAAM,EAAQ,QCXlC,CAIA,cAAe,UAAW,OAC2B,CAAnD,AAAoD,CAA7C,aAAA,AAAU,WAAV,AAAqB,QAArB,AAA6B,YCCtC,KAAM,UAAN,AAAe,YASf,AAAe,QAAA,AAAS,AAAsB,yBAAS,IACrD,AAAI,AAAO,SAKX,AAAI,AAAQ,YACN,GACK,EAAP,AAAO,AAAQ,6BACT,GAAY,UAAA,AAAU,EAA5B,AAAkB,AAAmB,OAC/B,EAAa,UAAA,AAAU,EAA7B,AAAmB,AAAmB,UACtC,AAAK,KAAL,AAAY,IACZ,AAAK,MAAL,AAAa,IACb,AAAK,QAAL,AAAe,IACf,AAAK,OAAL,AAAc,CAPhB,CAQE,AAAO,QAAK,CAThB,AAUO,QACE,EAAP,AAAO,AAAQ,6BAGX,GAAS,MACP,EADO,AACF,SACN,EAFQ,AAEH,UACH,EAAA,AAAK,MAAQ,EAHP,AAGY,YACjB,EAAA,AAAK,OAAS,EAJxB,AAI6B,KAIvB,EAAQ,AAAqB,MAArB,KAAA,AAAQ,SAAtB,AAAc,AAA8B,AAAmB,oBACzD,EACJ,EAAA,AAAM,OAAS,EAAf,AAAuB,aAAe,EAAA,AAAO,MAAQ,EADvD,AAC8D,KACxD,EACJ,EAAA,AAAM,QAAU,EAAhB,AAAwB,cAAgB,EAAA,AAAO,OAAS,EAD1D,AACiE,OAE7D,GAAiB,EAAA,AAAQ,YAA7B,AAA2C,EACvC,EAAgB,EAAA,AAAQ,aAA5B,AAA2C,KAIvC,GAAJ,AAAsB,EAAe,MAC7B,GAAS,yBAAf,AAAe,AAAyB,MACtB,eAAA,AAAe,EAAjC,AAAkB,AAAuB,QACxB,eAAA,AAAe,EAAhC,AAAiB,AAAuB,OAExC,AAAO,OAAP,AAAgB,IAChB,AAAO,QAAP,AAAiB,QAGZ,eAAP,AAAO,AAAc,GCzDvB,KAAM,QAAN,AAAe,YAEf,AAAe,QAAA,AAAS,AAAqC,AAAU,0CAAQ,MACvE,GAAN,AAAmC,MAApB,KAAA,AAAO,SAChB,EAAe,sBAArB,AAAqB,AAAsB,GACrC,EAAa,sBAAnB,AAAmB,AAAsB,GACnC,EAAe,gBAArB,AAAqB,AAAgB,MACjC,GAAU,cAAc,KACrB,EAAA,AAAa,IAAM,EADE,AACS,SAC7B,EAAA,AAAa,KAAO,EAFA,AAEW,WAC9B,EAHmB,AAGN,aACZ,EAJV,AAAc,AAIS,YAOnB,GAAJ,AAAkC,MAApB,KAAA,AAAO,SAAqB,MAClC,GAAS,yBAAf,AAAe,AAAyB,GAClC,EAAiB,QAAA,AAAU,EAAV,AACnB,EACA,CAAC,EAAA,AAAO,eAAP,AAAsB,MAAtB,AAA4B,MAFjC,AAEK,AAAkC,GACjC,EAAkB,QAAA,AAAU,EAAV,AACpB,EACA,CAAC,EAAA,AAAO,gBAAP,AAAuB,MAAvB,AAA6B,MAFlC,AAEK,AAAmC,GAClC,EAAY,QAAA,AAAU,EAAV,AAAmB,EAAI,CAAC,EAAA,AAAO,UAAP,AAAiB,MAAjB,AAAuB,MAAjE,AAA0C,AAA6B,GACjE,EAAa,QAAA,AAAU,EAAV,AAAmB,EAAI,CAAC,EAAA,AAAO,WAAP,AAAkB,MAAlB,AAAwB,MAAnE,AAA2C,AAA8B,KAEzE,AAAQ,KAAO,EAAf,AAAgC,IAChC,AAAQ,QAAU,EAAlB,AAAmC,IACnC,AAAQ,MAAQ,EAAhB,AAAkC,IAClC,AAAQ,OAAS,EAAjB,AAAmC,IAGnC,AAAQ,UAAR,AAAoB,IACpB,AAAQ,WAAR,AAAqB,QAIrB,GAAA,AAAO,SAAP,AAAgB,KACf,QAFH,AACE,AACqC,AACrC,MADW,KAAA,AAAa,cAEd,cAAA,AAAc,EAAxB,AAAU,AAAuB,IAGnC,AAAO,CChDT,CASA,AAAe,QAAA,AAAS,AAAoB,AAAO,AAAQ,2BAAW,MAC9D,GAAqB,uBAAA,AAAuB,EAAlD,AAA2B,AAA+B,SACnD,sCAAA,AAAqC,EAA5C,AAAO,AAAgD,ECfzD,CAOA,AAAe,QAAA,AAAS,AAAc,iBAAS,MACvC,GAAS,OAAA,AAAO,iBAAtB,AAAe,AAAwB,GACjC,EAAI,WAAW,EAAX,AAAkB,WAAa,WAAW,EAApD,AAAyC,AAAkB,cACrD,EAAI,WAAW,EAAX,AAAkB,YAAc,WAAW,EAArD,AAA0C,AAAkB,aACtD,EAAS,OACN,EAAA,AAAQ,YADF,AACgB,SACrB,EAAA,AAAQ,aAFlB,AAEiC,SAEjC,AAAO,ECfT,CAOA,AAAe,QAAA,AAAS,AAAqB,wBAAW,MAChD,GAAO,CAAE,KAAF,AAAQ,QAAS,MAAjB,AAAwB,OAAQ,OAAhC,AAAwC,MAAO,IAA5D,AAAa,AAAoD,gBAC1D,GAAA,AAAU,QAAV,AAAkB,yBAA0B,KAAW,EAA9D,AAAO,AAAuD,AAAK,GCNrE,CAUA,AAAe,QAAA,AAAS,AACtB,AACA,AACA,AACA,0BACA,GACY,EAAA,AAAU,MAAV,AAAgB,KAA5B,AAAY,AAAqB,QAG3B,GAAa,cAAnB,AAAmB,AAAc,GAG3B,EAAgB,CAAA,iBAEb,EAFa,AAEF,aACV,EAHV,AAGqB,QAIf,EAAmD,CAAzD,AAA0D,CAA1C,AAAC,AAAS,oBAAV,AAAkB,QAAlB,AAA0B,GACpC,EAAW,EAAA,AAAU,MAA3B,AAAmC,OAC7B,EAAgB,EAAA,AAAU,OAAhC,AAAyC,MACnC,EAAc,EAAA,AAAU,SAA9B,AAAyC,QACnC,EAAuB,AAAC,CAAD,CAA7B,AAAmD,QAAtB,AAAW,kBAExC,AAAc,GACZ,EAAA,AAAiB,GACjB,EAAA,AAAiB,GADjB,AACgC,EAChC,EAAA,AAAW,GAHb,AAG4B,IAE1B,AAAc,GADZ,IAAJ,AAAkB,AAAe,EAE7B,EAAA,AAAiB,GAAiB,EAFtC,AACE,AACoC,AAAW,AAC1C,AACL,AAAc,GACZ,EAAiB,qBADnB,AACE,AAAiB,AAAqB,IAG1C,AAAO,CClDT,CAOA,AAAe,QAAA,AAAS,AAAW,cAAiB,AAClD,AAAM,AAAU,OAEd,IADF,AAE6C,mBAA3C,MAAA,AAAQ,SAAR,AAAiB,KAAjB,AAAsB,GCT1B,QAAA,AAAS,AAAsB,AAAc,AAAO,AAAU,+BAAe,MACrE,GAAN,AAAyC,MAA1B,KAAA,AAAa,SACtB,EAAS,EAAA,AAAS,OAAxB,AAAiC,IACjC,AAAO,iBAAP,AAAwB,EAAxB,AAA+B,EAAU,CAAzC,AAAyC,AAAE,AAAS,AAEhD,aAAJ,AAAK,AAAQ,yBAET,gBAAgB,EADlB,AACE,AAAuB,YADzB,AAEE,EAFF,AAGE,EAHF,AAIE,KAGJ,AAAc,KAAd,AAAmB,GASrB,AAAe,QAAA,AAAS,AACtB,AACA,AACA,AACA,6BACA,GAEA,AAAM,YAAN,AAAoB,SACpB,AAAO,iBAAP,AAAwB,SAAU,EAAlC,AAAwC,YAAa,CAArD,AAAqD,AAAE,AAAS,kBAG1D,GAAgB,gBAAtB,AAAsB,AAAgB,gCACtC,AACE,EADF,AAEE,SACA,EAHF,AAGQ,YACN,EAJF,AAIQ,iBAER,AAAM,cAAN,AAAsB,IACtB,AAAM,AAAgB,iBAEtB,AAAO,CC7CT,CAMA,AAAe,QAAA,AAAS,AAAqB,AAAW,0BAAO,eAE7D,AAAO,oBAAP,AAA2B,SAAU,EAArC,AAA2C,eAG3C,AAAM,cAAN,AAAoB,QAAQ,KAAU,GACpC,AAAO,oBAAP,AAA2B,SAAU,EAArC,AAA2C,YAD7C,KAKA,AAAM,YAAN,AAAoB,OACpB,AAAM,AAAgB,mBACtB,AAAM,cAAN,AAAsB,OACtB,AAAM,AAAgB,iBACtB,AAAO,CCpBT,CASA,AAAe,QAAA,AAAS,AAAK,AAAK,UAAO,OAEnC,OAAA,AAAM,UAAV,AAAoB,AAAM,KACjB,EAAA,AAAI,KAAX,AAAO,AAAS,GAIX,EAAA,AAAI,OAAJ,AAAW,GAAlB,AAAO,AAAkB,ECd3B,CASA,AAAe,QAAA,AAAS,AAAU,AAAK,AAAM,iBAAO,IAE9C,MAAA,AAAM,UAAV,AAAoB,AAAW,gBACtB,GAAA,AAAI,UAAU,KAAO,EAAA,AAAI,KAAhC,AAAO,AAAmC,QAItC,GAAQ,KAAA,AAAK,EAAK,KAAO,EAAA,AAAI,KAAnC,AAAc,AAA+B,SACtC,GAAA,AAAI,QAAX,AAAO,AAAY,EChBrB,CAQA,AAAe,QAAA,AAAS,AAAa,AAAW,AAAM,oBAAM,MACpD,GAAiB,AAAS,WAAT,AACnB,EACA,EAAA,AAAU,MAAV,AAAgB,EAAG,UAAA,AAAU,EAAV,AAAqB,OAF5C,AAEI,AAAmB,AAA6B,aAEpD,AAAe,QAAQ,KAAY,CAC7B,EAAA,AAAS,SAAW,WAAW,EAAnC,AAAwB,AAAoB,AAAW,cAC9C,EAAA,AAAS,SAAT,AAAkB,EAAzB,AAAO,AAAwB,GAFnC,GAMA,AAAO,CCtBT,CAMA,AAAe,QAAA,AAAS,AAAkB,AAAW,uBAAc,OAC1D,GAAA,AAAU,KACf,CAAC,CAAA,AAAE,OAAH,AAAC,AAAQ,aAAc,GAAW,IADpC,AAAO,AACsC,GCHhC,QAAA,AAAS,AAA8C,iDAAS,MACvE,GAAO,OAAA,AAAO,SAApB,AAA6B,gBACvB,EAAiB,qCAAA,AAAqC,EAA5D,AAAuB,AAA8C,GAC/D,EAAQ,AAAK,SAAI,EAAT,AAAc,YAAa,OAAA,AAAO,YAAhD,AAAc,AAAgD,GACxD,EAAS,AAAK,SAAI,EAAT,AAAc,aAAc,OAAA,AAAO,aAAlD,AAAe,AAAkD,GAE3D,EAAY,UAAlB,AAAkB,AAAU,GACtB,EAAa,UAAA,AAAU,EAA7B,AAAmB,AAAgB,QAE7B,EAAS,KACR,EAAY,EAAZ,AAA2B,IAAM,EADzB,AACwC,eAC/C,EAAa,EAAb,AAA4B,KAAO,EAF5B,AAE2C,WAF3C,QAAf,gBAOO,eAAP,AAAO,AAAc,EClBvB,CAQA,AAAe,QAAA,AAAS,AAAQ,WAAS,MACjC,GAAW,EAAjB,AAAyB,eACrB,AAAa,MAAb,MAAJ,AAAwC,AAAQ,AAC9C,AAAO,MADkB,UAG3B,AAAsD,AAAS,AAC7D,AAAO,OADL,4BAAA,AAAyB,EAAzB,AAAkC,cAG/B,QAAQ,cAAf,AAAO,AAAQ,AAAc,GCT/B,CASA,AAAe,QAAA,AAAS,AACtB,AACA,AACA,AACA,uBACA,IAEI,GAAa,CAAE,IAAF,AAAO,EAAG,KAA3B,AAAiB,AAAgB,QAC3B,GAAe,uBAAA,AAAuB,EAA5C,AAAqB,AAA+B,MAGpD,AAA0B,AAAY,UAAlC,OACW,8CADf,AACE,AAAa,AAA8C,OACtD,IAEL,AAAI,GACJ,AAA0B,AAAgB,cAAtC,QACe,gBAAgB,cAAjC,AAAiB,AAAgB,AAAc,IAC/C,AAAgC,AAAQ,MAApC,KAAA,AAAe,aACA,OAAA,AAAO,SAH5B,AAGI,AAAiC,AAE9B,kBAAA,AAA0B,AAAU,QAAhC,OACQ,OAAA,AAAO,SADnB,AACL,AAAiC,AAC5B,kBACL,AAAiB,OAGb,GAAU,qCAAA,AACd,EADF,AAAgB,AAEd,MAIE,AAA4B,MAA5B,KAAA,AAAe,UAAuB,CAAC,QAA3C,AAA2C,AAAQ,GAAe,MAC1D,CAAA,AAAE,SAAF,AAAU,SAAhB,AAA0B,mBAC1B,AAAW,KAAO,EAAA,AAAQ,IAAM,EAAhC,AAAwC,YACxC,AAAW,OAAS,EAAS,EAA7B,AAAqC,MACrC,AAAW,MAAQ,EAAA,AAAQ,KAAO,EAAlC,AAA0C,aAC1C,AAAW,MAAQ,EAAQ,EAA3B,AAAmC,IALrC,AAMO,QAEL,AAAa,WAKjB,AAAW,MAAX,AAAmB,IACnB,AAAW,KAAX,AAAkB,IAClB,AAAW,OAAX,AAAoB,IACpB,AAAW,QAAX,AAAqB,EAErB,AAAO,CCpET,CASA,AAAe,QAAA,AAAS,AACtB,AACA,AACA,AACA,8BACA,IACkC,CAAlC,AAAmC,AAAG,CAAlC,KAAA,AAAU,QAAV,AAAkB,cACpB,AAAO,QAGH,GAAa,cAAA,AAAc,EAAd,AAAsB,EAAtB,AAAiC,EAApD,AAAmB,AAAoC,gBAEjD,EAAQ,KACP,EAAA,AAAQ,IAAM,EADP,AACkB,UACvB,EAAA,AAAW,MAAQ,EAFd,AAEsB,aAC1B,EAAA,AAAW,OAAS,EAHhB,AAGwB,YAC9B,EAAA,AAAQ,KAAO,EAJvB,AAIkC,MAG5B,EAAoB,OAAA,AAAO,KAAP,AAAY,GAAZ,AAAmB,KAC3C,AAAC,AAAG,OAAM,EAAA,AAAM,GAAK,EADG,AACH,AAAM,IAD7B,AAA0B,AAExB,GACI,EAAY,EAAA,AAAU,MAAV,AAAgB,KAAlC,AAAkB,AAAqB,SAEhC,IAAqB,AAAa,MAAG,AAAU,CAA1B,GAA5B,AAAO,AAAmD,ICnC5D,KAAA,AAAM,AAAa,AACjB,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAGF,AClBA,+KAOA,AAAe,QAAA,AAAS,AAAc,AAAS,mBAAY,QACzD,AAAO,KAAP,AAAY,GAAZ,AAAwB,QAAQ,AAAS,WAAM,MACvC,GAAQ,EAAd,AAAc,AAAW,GACzB,AAAI,AAAU,AAAO,SAGnB,AAAQ,gBAAR,AAAwB,KAFxB,AAAQ,aAAR,AAAqB,EAAM,EAD7B,AACE,AAA2B,AAAW,AACjC,GAJT,ECFF,CAUA,AAAe,QAAA,AAAS,AAAW,AAAM,gBAAS,MAG1C,GAAS,UACH,EAAA,AAAK,QAAL,AAAa,OADzB,AACgC,UAG1B,EAAa,eACF,EADjB,AACsB,WAIhB,aAAkB,EAAA,AAAK,QAAL,AAAa,OAArC,AAAa,AAA+B,MACtC,EAAM,AAAK,WAAM,EAAA,AAAK,QAAL,AAAa,OAApC,AAAY,AAA+B,KAKrC,EAAmB,yBAAzB,AAAyB,AAAyB,mBAC9C,GAAA,AAAQ,iBAAZ,AAA+B,AAAkB,KAC/C,AAAO,GAAoB,eAAA,AAAiB,EAAjB,AAAwB,OAAxB,AAAiC,EAA5D,AAAkE,WAClE,AAAO,IAAP,AAAa,IACb,AAAO,KAAP,AAAc,IACd,AAAO,WAJT,AAIE,AAAoB,AACf,gBAEL,AAAO,KAAP,AAAc,IACd,AAAO,IAAP,AAAa,IACb,AAAO,WAAP,AAAoB,uBAOZ,EAAA,AAAK,SAAf,AAAwB,mBAAxB,AAAqC,EAAW,EAAhD,AAAqD,uBAIvC,EAAA,AAAK,SAAnB,AAA4B,mBAA5B,AAAyC,EAAe,EAAxD,AAA6D,aAGzD,EAAA,AAAK,QAAT,AAAiB,AAAO,iBACZ,EAAV,AAAe,aAAc,EAAA,AAAK,QAAlC,AAA0C,OAG5C,AAAO,EAYT,AAAO,QAAA,AAAS,AACd,AACA,AACA,AACA,AACA,4BACA,MAEM,GAAmB,oBAAA,AAAoB,EAApB,AAA2B,EAApD,AAAyB,AAAmC,YAK5D,AAAQ,UAAY,qBAClB,EADkB,AACV,UADU,AAElB,EAFkB,AAGlB,EAHF,AAAoB,AAIlB,KAGF,AAAO,aAAP,AAAoB,cAAe,EAAnC,AAA2C,WAC3C,AAAO,CC7FT,CAUA,AAAe,QAAA,AAAS,AACtB,AACA,AACA,0BACA,MACM,GAAa,KAAA,AAAK,EAAW,CAAC,CAAD,AAAC,AAAE,UAAW,IAAjD,AAAmB,AAAuC,SAGxD,CAAC,CAAD,AAAE,GACF,EAAA,AAAU,KAAK,KAAY,OAEvB,GAAA,AAAS,OAAT,AAAkB,GAClB,EADA,AACS,SACT,EAAA,AAAS,MAAQ,EAHnB,AAG8B,KANlC,AAEE,ECjBJ,CASA,AAAe,QAAA,AAAS,AAAM,AAAM,WAAS,IAEvC,CAAC,mBAAmB,EAAA,AAAK,SAAxB,AAAiC,UAAjC,AAA4C,QAAjD,AAAK,AAAqD,AAAiB,+BACzE,AAAQ,KAAR,AACE,0HAEF,AAAO,KAGL,GAAe,EAAnB,AAA2B,WAG3B,AAA4B,AAAU,QAAlC,QAAA,AAAO,SACM,EAAA,AAAK,SAAL,AAAc,OAAd,AAAqB,cAApC,AAAe,AAAmC,GAG9C,CAAJ,AAAK,AAAc,QAJrB,AAKI,AAAO,AAEJ,YAGD,CAAC,EAAA,AAAK,SAAL,AAAc,OAAd,AAAqB,SAA1B,AAAK,AAA8B,AAAe,kBAChD,AAAQ,KAAR,AACE,iEAEF,AAAO,OAIL,GAAY,EAAA,AAAK,UAAL,AAAe,MAAf,AAAqB,KAAvC,AAAkB,AAA0B,GACtC,EAAS,cAAc,EAAA,AAAK,QAAlC,AAAe,AAA2B,QACpC,EAAY,EAAA,AAAK,QAAvB,AAA+B,UACzB,EAAsD,CAA5D,AAA6D,CAA1C,AAAC,AAAQ,oBAAT,AAAkB,QAAlB,AAA0B,GAEvC,EAAM,EAAA,AAAa,SAAzB,AAAoC,QAC9B,EAAO,EAAA,AAAa,MAA1B,AAAkC,OAC5B,EAAU,EAAA,AAAa,OAA7B,AAAsC,MAChC,EAAS,EAAA,AAAa,SAA5B,AAAuC,QACjC,EAAmB,cAAA,AAAc,GAAvC,AAAyB,AAA4B,GAOjD,EAAA,AAAU,GAAV,AAAoB,EAAmB,EAA3C,AAA2C,AAAO,AAAO,OACvD,AAAK,QAAL,AAAa,OAAb,AAAoB,IAClB,EAAA,AAAO,IAAS,EAAA,AAAU,GAD5B,AACE,AAAoC,IAGpC,EAAA,AAAU,GAAV,AAAkB,EAAmB,EAAzC,AAAyC,AAAO,AAAS,OACvD,AAAK,QAAL,AAAa,OAAb,AAAoB,IAClB,EAAA,AAAU,GAAV,AAAkB,EAAmB,EADvC,AACuC,AAAO,SAI1C,GAAS,EAAA,AAAU,GAAQ,EAAA,AAAU,GAA5B,AAAmC,EAAI,EAAtD,AAAyE,KAGrE,GAAY,EAAS,cAAc,EAAA,AAAK,QAAnB,AAA2B,QAApD,AAAyB,AAAmC,YAGhD,AAAK,kBAAa,EAAA,AAAO,GAAhB,AAAuB,EAAhC,AAAS,AAAyC,GAA9D,AAAY,AAA8D,KAE1E,AAAK,aAAL,AAAoB,IACpB,AAAK,QAAL,AAAa,AAAQ,WACrB,AAAK,QAAL,AAAa,MAAb,AAAmB,GAAnB,AAA2B,IAC3B,AAAK,QAAL,AAAa,MAAb,AAAmB,GApEwB,AAoE3C,AAA8B,GAE9B,AAAO,CCnFT,CAOA,AAAe,QAAA,AAAS,AAAqB,wBAAW,IACtD,AAAkB,AAAO,KAArB,WAAJ,AACE,AAAO,AACF,cAAA,AAAkB,AAAS,OAAvB,KACT,AAAO,MAET,AAAO,CCXT,CACA,KAAM,iBAAkB,WAAA,AAAW,MAAnC,AAAwB,AAAiB,GAYzC,AAAe,QAAA,AAAS,AAAU,aAAnB,AAA8B,AAAU,KAAO,MACtD,GAAQ,gBAAA,AAAgB,QAA9B,AAAc,AAAwB,GAChC,EAAM,gBAAA,AACT,MAAM,EADG,AACK,GADL,AAET,OAAO,gBAAA,AAAgB,MAAhB,AAAsB,EAFhC,AAAY,AAEF,AAAyB,UAC5B,GAAU,EAAV,AAAU,AAAI,UAArB,AAAiC,ECXnC,KAAM,WAAY,MAAA,AACV,iBADU,AAEL,6BAFb,AAGoB,oBAapB,AAAe,QAAA,AAAS,AAAK,AAAM,UAAS,IAEtC,kBAAkB,EAAA,AAAK,SAAvB,AAAgC,UAApC,AAAI,AAA2C,AAAU,eACvD,AAAO,MAGL,EAAA,AAAK,SAAW,EAAA,AAAK,YAAc,EAAvC,AAA4C,AAAmB,wBAE7D,AAAO,QAGH,GAAa,cACjB,EAAA,AAAK,SADY,AACH,OACd,EAAA,AAAK,SAFY,AAEH,UACd,EAHiB,AAGT,QACR,EAJF,AAAmB,AAIT,sBAGN,GAAY,EAAA,AAAK,UAAL,AAAe,MAAf,AAAqB,KAArC,AAAgB,AAA0B,GACtC,EAAoB,qBAAxB,AAAwB,AAAqB,GACzC,EAAY,EAAA,AAAK,UAAL,AAAe,MAAf,AAAqB,KAArB,AAA0B,IAA1C,AAAgD,GAEhD,AAAI,AAAY,YAER,EAAR,AAAgB,cACT,WAAL,AAAe,OACD,CAAA,AAAC,EAAb,AAAY,AAAY,aAErB,WAAL,AAAe,YACD,UAAZ,AAAY,AAAU,aAEnB,WAAL,AAAe,mBACD,UAAZ,AAAY,AAAU,AAAW,sBAGrB,EAAZ,AAAoB,mBAGxB,AAAU,QAAQ,AAAC,AAAM,OAAU,IAC7B,IAAA,AAAc,GAAQ,EAAA,AAAU,SAAW,EAA/C,AAAuD,AAAG,QACxD,AAAO,KAGG,EAAA,AAAK,UAAL,AAAe,MAAf,AAAqB,KAAjC,AAAY,AAA0B,KAClB,qBAApB,AAAoB,AAAqB,QAEnC,GAAgB,cAAc,EAAA,AAAK,QAAzC,AAAsB,AAA2B,QAC3C,EAAa,EAAA,AAAK,QAAxB,AAAgC,UAG1B,aACA,EACH,AAAc,MAAd,MACC,EAAM,EAAN,AAAoB,OAAS,EAAM,EADrC,AAC+B,AAAiB,OAC/C,AAAc,OAAd,MACC,EAAM,EAAN,AAAoB,MAAQ,EAAM,EAHpC,AAG8B,AAAiB,QAC9C,AAAc,KAAd,MACC,EAAM,EAAN,AAAoB,QAAU,EAAM,EALtC,AAKgC,AAAiB,MAChD,AAAc,QAAd,MACC,EAAM,EAAN,AAAoB,KAAO,EAAM,EARrC,AAQ+B,AAAiB,QAE1C,EAAgB,EAAM,EAAN,AAAoB,MAAQ,EAAM,EAAxD,AAAkD,AAAiB,MAC7D,EAAiB,EAAM,EAAN,AAAoB,OAAS,EAAM,EAA1D,AAAoD,AAAiB,OAC/D,EAAe,EAAM,EAAN,AAAoB,KAAO,EAAM,EAAtD,AAAgD,AAAiB,KAC3D,EACJ,EAAM,EAAN,AAAoB,QAAU,EAAM,EADtC,AACgC,AAAiB,QAE3C,EACH,AAAc,MAAd,MAAD,AAAyB,GACxB,AAAc,OAAd,MADD,AAC0B,GACzB,AAAc,KAAd,MAFD,AAEwB,GACvB,AAAc,QAAd,MAJH,AAI6B,EAGvB,EAAsD,CAA5D,AAA6D,CAA1C,AAAC,AAAO,oBAAR,AAAkB,QAAlB,AAA0B,GACvC,EACJ,CAAC,CAAC,EAAF,AAAU,iBACR,GAAA,AAA4B,OAAd,MAAf,AAAwC,GACtC,GAAA,AAA4B,KAAd,MADhB,AACuC,GACrC,CAAA,AAAC,GAAD,AAA6B,OAAd,MAFjB,AAE0C,GACxC,CAAA,AAAC,GAAD,AAA6B,KAAd,MALpB,AACE,AAIyC,IAEvC,GAAA,AAAe,GAAnB,AAA0C,AAAkB,OAE1D,AAAK,AAAU,YAEX,GAAJ,AAAmB,AAAqB,OAC1B,EAAU,EAAtB,AAAY,AAAkB,IAGhC,AAAI,AAAkB,MACR,qBAAZ,AAAY,AAAqB,MAGnC,AAAK,UAAY,GAAa,EAAY,IAAZ,AAAkB,EAAhD,AAAiB,AAA2C,MAC5D,AAAK,QAAL,AAAa,OAAS,iBACpB,EAAA,AAAK,SAAL,AAAc,MADM,AACA,SACpB,EAAA,AAAK,SAFe,AAEN,OACd,EAAA,AAAK,QAHe,AAGP,UACb,EAJF,AAAsB,AAIf,aAGA,aAAa,EAAA,AAAK,SAAlB,AAA2B,UAA3B,AAAsC,EAA7C,AAAO,AAA4C,QAhEvD,GAmEA,AAAO,CChIT,CAUA,AAAe,QAAA,AAAS,AAAa,gBAAM,MACnC,GAAS,cAAc,EAAA,AAAK,QAAlC,AAAe,AAA2B,QACpC,EAAY,EAAA,AAAK,QAAvB,AAA+B,UACzB,EAAY,EAAA,AAAK,UAAL,AAAe,MAAf,AAAqB,KAAvC,AAAkB,AAA0B,GAC5C,AAAM,AAAQ,AAAK,aACb,EAAsD,CAA5D,AAA6D,CAA1C,AAAC,AAAO,oBAAR,AAAkB,QAAlB,AAA0B,GACvC,EAAO,EAAA,AAAa,QAA1B,AAAoC,SAC9B,EAAS,EAAA,AAAa,OAA5B,AAAqC,MAC/B,EAAc,EAAA,AAAa,QAAjC,AAA2C,eAEvC,GAAA,AAAO,GAAQ,EAAM,EAAzB,AAAmB,AAAM,AAAU,AAAU,QAC3C,AAAK,QAAL,AAAa,OAAb,AAAoB,GAClB,EAAM,EAAN,AAAM,AAAU,IAAW,EAD7B,AAC6B,AAAO,IAElC,EAAA,AAAO,GAAU,EAAM,EAA3B,AAAqB,AAAM,AAAU,AAAQ,QAC3C,AAAK,QAAL,AAAa,OAAb,AAAoB,GAAU,EAAM,EAApC,AAA8B,AAAM,AAAU,KAGhD,AAAO,CC3BT,CAkBA,AAAe,QAAA,AAAS,AAAO,AAAM,YAAS,MACtC,GAAY,EAAlB,AAAuB,UACjB,EAAS,EAAA,AAAK,QAApB,AAA4B,UAE5B,AAAI,SACA,WAAU,EAAd,AAAI,AAAkB,AAAS,UACnB,CAAC,EAAD,AAAS,OADrB,AACE,AAAU,AAAiB,AACtB,MAGK,EAAA,AAAQ,OAAR,AAAe,MAAzB,AAAU,AAAqB,OAGrB,EAAA,AAAQ,IAAI,AAAC,AAAQ,OAAU,MAEjC,GAAQ,EAAA,AAAO,MAArB,AAAc,AAAa,mBACrB,EAAQ,CAAC,EAAf,AAAe,AAAM,GACf,EAAO,EAAb,AAAa,AAAM,MAKf,GAC6B,CAA/B,AAAgC,CAAhC,KAAA,AAAU,QAAV,AAAkB,UAAiD,CADrE,AACsE,CAA/B,KAAA,AAAU,QAAV,AAAkB,QAEzD,AAAc,AAAG,CAAb,SACU,CAAZ,AAAa,QAGT,GAAc,EAAA,AAAY,SAAhC,AAA2C,WAM3C,AAA0B,CAAtB,KAAA,AAAK,QAAL,AAAa,KAAY,IAC3B,AAAI,UACJ,AAAQ,OACN,AAAK,OACO,EAAA,AAAK,QAAf,AAAuB,iBAEzB,AAAK,QACL,AAAK,eAEO,EAAA,AAAK,QAAf,AAAuB,gBAGrB,GAAO,cAAb,AAAa,AAAc,GACrB,EAAM,EAAZ,AAAY,AAAK,SACV,GAAA,AAAM,IAAb,AAAmB,CAdrB,CAeO,GAAI,AAAS,IAAT,MAAJ,AAA8B,IAAT,KAAe,IAEzC,AAAI,YACJ,AAAa,AAAM,IAAf,KACK,AAAK,SACV,SAAA,AAAS,gBADJ,AACoB,aACzB,OAAA,AAAO,aAHX,AACE,AAAO,AAEiB,AAEnB,GACE,AAAK,SACV,SAAA,AAAS,gBADJ,AACoB,YACzB,OAAA,AAAO,YAFT,AAAO,AAEgB,GAGlB,EAAA,AAAO,IAAd,AAAoB,CAdf,AAeA,OAAA,AAAa,AAAM,IAAf,KAEF,CAFF,AAEL,AAAQ,AACH,EAEE,CAAP,AAAQ,CAzDZ,AAAU,IA8D2B,CAAvC,AAAwC,AAAG,CAAvC,KAAA,AAAK,UAAL,AAAe,QAAf,AAAuB,QAGoB,CAAxC,AAAyC,AAAG,CAAxC,KAAA,AAAK,UAAL,AAAe,QAAf,AAAuB,SAGW,CAAtC,AAAuC,AAAG,CAAtC,KAAA,AAAK,UAAL,AAAe,QAAf,AAAuB,OAGc,CAAzC,AAA0C,AAAG,CAAzC,KAAA,AAAK,UAAL,AAAe,QAAf,AAAuB,cAChC,AAAO,MAAQ,EAAf,AAAe,AAAQ,KACvB,AAAO,KAAO,EAAA,AAAQ,IAAtB,AAA4B,MAJ5B,AAAO,MAAQ,EAAf,AAAe,AAAQ,KACvB,AAAO,KAAO,EAAA,AAAQ,IAFjB,AAEL,AAA4B,AACvB,MALL,AAAO,KAAO,EAAd,AAAc,AAAQ,KACtB,AAAO,MAAQ,EAAA,AAAQ,IAFlB,AAEL,AAA6B,AACxB,MALL,AAAO,KAAO,EAAd,AAAc,AAAQ,KACtB,AAAO,MAAQ,EAAA,AAAQ,IAFzB,AAEE,AAA6B,AACxB,GAUP,AAAO,CCzGT,CAoBA,AAAe,QAAA,AAAS,AAAgB,AAAM,qBAAS,MAC/C,GACJ,EAAA,AAAQ,mBAAqB,gBAAgB,EAAA,AAAK,SADpD,AAC+B,AAA8B,QACvD,EAAa,cACjB,EAAA,AAAK,SADY,AACH,OACd,EAAA,AAAK,SAFY,AAEH,UACd,EAHiB,AAGT,QAHV,AAAmB,AAIjB,KAEF,AAAQ,WAAR,AAAqB,OAEf,GAAQ,EAAd,AAAsB,YAClB,GAAS,cAAc,EAAA,AAAK,QAAhC,AAAa,AAA2B,aAElC,GAAQ,CACZ,AAAQ,UAAW,IACb,GAAQ,EAAZ,AAAY,AAAO,SAEjB,GAAA,AAAO,GAAa,EAApB,AAAoB,AAAW,IAC/B,CAAC,EAFH,AAEW,AACT,wBACQ,AAAK,SAAI,EAAT,AAAS,AAAO,GAAY,EAApC,AAAQ,AAA4B,AAAW,KAE1C,CAAE,CAAA,AAAC,GAAV,AAAO,AAAe,EATZ,EAWZ,AAAU,YAAW,MACb,GAAW,AAAc,OAAd,KAAA,AAAwB,OAAzC,AAAkD,SAC9C,GAAQ,EAAZ,AAAY,AAAO,SAEjB,GAAA,AAAO,GAAa,EAApB,AAAoB,AAAW,IAC/B,CAAC,EAFH,AAEW,AACT,wBACQ,AAAK,SACX,EADM,AACN,AAAO,GACP,EAAA,AAAW,IACR,AAAc,OAAd,KAAwB,EAAxB,AAA+B,MAAQ,EAH5C,AAAQ,AAEN,AACiD,UAG9C,CAAE,CAAA,AAAC,GAAV,AAAO,AAAc,EAxBzB,YA4BA,AAAM,QAAQ,KAAa,MACnB,GAA8C,CAAvC,AAAwC,CAAxC,AAAC,AAAQ,kBAAT,AAAgB,QAAhB,AAAwB,GAArC,AAEI,YAFS,AACT,wBAEJ,AAAc,EAAW,EAAA,AAAM,GAA/B,AAAyB,AAAY,GAJvC,KAOA,AAAK,QAAL,AAAa,OAAb,AAAsB,EAEtB,AAAO,CCzET,CAQA,AAAe,QAAA,AAAS,AAAM,SAAM,MAC5B,GAAY,EAAlB,AAAuB,UACjB,EAAgB,EAAA,AAAU,MAAV,AAAgB,KAAtC,AAAsB,AAAqB,GACrC,EAAiB,EAAA,AAAU,MAAV,AAAgB,KAAvC,AAAuB,AAAqB,MAG5C,AAAI,EAAgB,MACZ,GAAY,EAAA,AAAK,QAAvB,AAA+B,UACzB,EAAS,cAAc,EAAA,AAAK,QAAlC,AAAe,AAA2B,QACpC,EAA0D,CAAhE,AAAiE,CAA9C,AAAC,AAAU,oBAAX,AAAkB,QAAlB,AAA0B,GACvC,EAAO,EAAA,AAAa,OAA1B,AAAmC,MAC7B,EAAc,EAAA,AAAa,QAAjC,AAA2C,SAErC,EAAe,OACZ,CAAE,CAAA,AAAC,GAAO,EADE,AACZ,AAAU,AAAU,QACtB,EACH,AAAC,GAAO,EAAA,AAAU,GAAQ,EAAlB,AAAkB,AAAU,GAAe,EAHvD,AAGuD,AAAO,OAI9D,AAAK,QAAL,AAAa,mBAAb,AAA2B,EAAW,EAAtC,AAAsC,AAAa,UAGrD,AAAO,EC9BT,CAUA,AAAe,QAAA,AAAS,AAAK,QAAM,IAC7B,CAAC,mBAAmB,EAAA,AAAK,SAAxB,AAAiC,UAAjC,AAA4C,OAAjD,AAAK,AAAoD,AAAoB,kCAC3E,AAAQ,KAAR,AACE,uHAEF,AAAO,OAGH,GAAU,EAAA,AAAK,QAArB,AAA6B,UACvB,EAAQ,KACZ,EAAA,AAAK,SADO,AACE,UACd,KAFY,AAEkB,iBAAlB,KAAA,AAAS,MAFvB,AAGE,cAGA,EAAA,AAAQ,OAAS,EAAjB,AAAuB,KACvB,EAAA,AAAQ,KAAO,EADf,AACqB,OACrB,EAAA,AAAQ,IAAM,EAFd,AAEoB,QACpB,EAAA,AAAQ,MAAQ,EAJlB,AAIwB,KACtB,IAEI,OAAJ,AAAI,AAAK,AAAS,AAAM,WACtB,AAAO,KAGT,AAAK,AAAO,UACZ,AAAK,WAAL,AAAgB,uBAAhB,AAAyC,EAZ3C,KAaO,IAED,OAAJ,AAAI,AAAK,AAAS,AAAO,WACvB,AAAO,KAGT,AAAK,AAAO,UACZ,AAAK,WAAL,AAAgB,AAAyB,gCAG3C,AAAO,EC/CT,CAUA,AAAe,QAAA,AAAS,AAAM,SAAM,MAC5B,GAAY,EAAlB,AAAuB,UACjB,EAAgB,EAAA,AAAU,MAAV,AAAgB,KAAtC,AAAsB,AAAqB,GACrC,EAAS,cAAc,EAAA,AAAK,QAAlC,AAAe,AAA2B,QACpC,EAAY,cAAc,EAAA,AAAK,QAArC,AAAkB,AAA2B,WACvC,EAAuD,CAA7D,AAA8D,CAA9C,AAAC,AAAQ,oBAAT,AAAkB,QAAlB,AAA0B,GAEpC,EAA4D,CAAlE,AAAmE,CAA5C,AAAC,AAAO,kBAAR,AAAgB,QAAhB,AAAwB,YAExC,EAAA,AAAU,OAAjB,AAA0B,OACxB,EAAA,AAAU,IACT,EAAiB,EAAO,EAAA,AAAU,QAAlC,AAAiB,AAA2B,UAF/C,AACE,AACyD,KAE3D,AAAK,UAAY,qBAAjB,AAAiB,AAAqB,KACtC,AAAK,QAAL,AAAa,OAAS,cAAtB,AAAsB,AAAc,GAEpC,AAAO,CCnBT,CASA,cAAe,OAMN,OAAA,AAEE,IAFF,AAII,oBAVE,AAYD,cASJ,OAAA,AAEC,IAFD,AAIG,oBAJH,AAMI,cA3BC,AAsCH,mBAmBO,OAAA,AAER,IAFQ,AAIN,oBAJM,AAML,gBANK,AAYL,AAAC,AAAQ,AAAS,AAAO,iDAZpB,AAmBN,oBA5EE,AAkFQ,6BAYP,OAAA,AAEL,IAFK,AAIH,oBAlGE,AAoGD,oBAWL,OAAA,AAEE,IAFF,AAII,oBAJJ,AAMK,cArHC,AAuHF,kBAYL,OAAA,AAEG,IAFH,AAIK,oBAJL,AAMM,cANN,AAaM,eAbN,AAkBK,oBArJE,AA4JQ,kBAUd,OAAA,AAEE,IAFF,AAII,oBA1KE,AA4KD,YAUN,OAAA,AAEG,IAFH,AAIK,oBA1LE,AA4LD,iBAUA,OAAA,AAEH,IAFG,AAID,oBAJC,AAMA,kBANA,AAQF,iBCjOZ,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AACA,AAEA,AACA,AAEA,ADEA,AAoNqB,qBC5LrB,KAAM,UAAW,WAAA,AAMJ,SANI,AAaA,iBAbA,AAqBE,4BASP,IAAM,CA9BD,WAyCL,IAAM,CAzCD,EAAjB,WA4DA,AAAe,KAAA,AAAM,OAAO,CAC1B,AAAY,AAAW,gBAAvB,AAA+B,AAAU,KAAI,MAAA,AAyJ7C,eAAiB,IAAM,sBAAsB,KAzJA,AAyJtB,AAA2B,aAvJhD,AAAK,OAAS,SAAS,KAAA,AAAK,OAAL,AAAY,KAAnC,AAAc,AAAS,AAAiB,YAGxC,AAAK,oBAAe,OAApB,AAA2B,SAA3B,AAAwC,QAGxC,AAAK,MAAQ,CAAA,AACE,eADF,AAEA,aAFb,AAGiB,uBAIjB,AAAK,UAAY,EAAA,AAAU,OAAS,EAAnB,AAAmB,AAAU,GAA9C,AAAmD,OACnD,AAAK,OAAS,EAAA,AAAO,OAAS,EAAhB,AAAgB,AAAO,GAArC,AAA0C,YAGhC,KAAV,AAAe,OAAQ,CAAE,SAAzB,AAAuB,AAAY,kBAGnC,AAAK,UAAY,OAAA,AAAO,KAAK,OAAA,AAAO,SAAnB,AAA4B,WAA5B,AAAuC,IAAI,uBAEvD,OAAA,AAAO,SAAP,AAAgB,UAFrB,AAAiB,AAA2C,AAEvD,AAA0B,UAK/B,AAAK,UAAY,KAAA,AAAK,UAAL,AAAe,IAAI,KAAiB,MAC7C,GAAc,EAAA,AAAQ,WAC1B,EAAA,AAAQ,UAAU,EADpB,AAAmB,AACjB,AAAgC,AAAU,6BAC5C,AAAY,EAAZ,AAA8B,EAHhC,AAAiB,GAOb,EAAJ,AAAY,AAAW,iBACrB,AAAK,QAAL,AAAa,UAAb,AACK,YACA,OAAA,AAAO,SAFZ,AAEqB,UAChB,EAHL,AAGa,kBAEb,AAAO,KAAK,EAAZ,AAAoB,WAApB,AAA+B,QAAQ,KAAQ,IAEzC,OAAA,AAAO,SAAP,AAAgB,UAApB,AAAI,AAA0B,AAAU,YAAW,MAC3C,GAAW,EAAA,AAAQ,UAAzB,AAAiB,AAAkB,KACnC,AAAS,KAAT,AAAgB,OAChB,AAAK,UAAL,AAAe,KAAf,AAAoB,GALxB,SAWF,AAAK,UAAY,KAAA,AAAK,UAAL,AAAe,KAAK,AAAC,AAAG,OAAM,EAAA,AAAE,MAAQ,EAAzD,AAAiB,AAA0C,YAM3D,AAAK,UAAL,AAAe,QAAQ,KAAmB,CACpC,EAAA,AAAgB,SAAW,WAAW,EAA1C,AAA+B,AAA2B,AAAS,WACjE,AAAgB,OACd,KADF,AACO,UACL,KAFF,AAEO,OACL,KAHF,AAGO,QAHP,AAIE,EACA,KALF,AAKO,MAPX,QAaA,AAAK,cAEC,GAAgB,KAAA,AAAK,QAA3B,AAAmC,cACnC,AAAI,AAAe,QAEjB,AAAK,4BAGP,AAAK,MAAL,AAAW,cAAX,AAA2B,UAapB,IAEH,KAAA,AAAK,MAAT,AAAe,AAAa,sBAIxB,GAAO,UAAA,AACC,KADD,AAED,UAFC,AAGG,cAHH,AAIA,WAJX,AAKW,cAIX,AAAK,QAAL,AAAa,UAAY,oBACvB,KADuB,AAClB,MACL,KAFuB,AAElB,OACL,KAHF,AAAyB,AAGlB,aAMP,AAAK,UAAY,qBACf,KAAA,AAAK,QADU,AACF,UACb,EAAA,AAAK,QAFU,AAEF,UACb,KAHe,AAGV,OACL,KAJF,AAAiB,AAIV,aAIP,AAAK,kBAAoB,KAAA,AAAK,QAA9B,AAAsC,YAGtC,AAAK,QAAL,AAAa,OAAS,iBACpB,KADoB,AACf,MACL,KAFoB,AAEf,OACL,EAAA,AAAK,QAHe,AAGP,UACb,EAJF,AAAsB,AAIf,aAIA,aAAa,KAAb,AAAkB,UAAzB,AAAO,AAA6B,AAIhC,GAAC,KAAA,AAAK,MAAV,AAAgB,AAAW,eAIzB,AAAK,QAAL,AAAa,SAAb,AAAsB,SAHtB,AAAK,MAAL,AAAW,AAAY,kBACvB,AAAK,QAAL,AAAa,SAFf,AAEE,AAAsB,AACjB,aAiBC,aACR,AAAK,MAAL,AAAW,AAAc,eAGrB,kBAAkB,KAAlB,AAAuB,UAA3B,AAAI,AAAkC,AAAe,qBACnD,AAAK,OAAL,AAAY,gBAAZ,AAA4B,oBAC5B,AAAK,OAAL,AAAY,MAAZ,AAAkB,KAAlB,AAAyB,QACzB,AAAK,OAAL,AAAY,MAAZ,AAAkB,SAAlB,AAA6B,QAC7B,AAAK,OAAL,AAAY,MAAZ,AAAkB,IAAlB,AAAwB,QACxB,AAAK,OAAL,AAAY,MAAM,yBAAlB,AAAkB,AAAyB,cAA3C,AAA2D,SAG7D,AAAK,wBAID,KAAA,AAAK,QAAT,AAAiB,AAAiB,sBAChC,AAAK,OAAL,AAAY,WAAZ,AAAuB,YAAY,KAAnC,AAAwC,QAE1C,AAAO,2BASc,AACjB,CAAC,KAAA,AAAK,MAAV,AAAgB,AAAe,qBAC7B,AAAK,MAAQ,oBACX,KADW,AACN,UACL,KAFW,AAEN,QACL,KAHW,AAGN,MACL,KAJF,AAAa,AAIN,wCAYa,CAClB,KAAA,AAAK,MAAT,AAAe,AAAe,uBAC5B,AAAO,qBAAqB,KAA5B,AAAiC,qBACjC,AAAK,MAAQ,qBAAqB,KAArB,AAA0B,UAAW,KAAlD,AAAa,AAA0C,SAlNxC,OA4NZ,MAAQ,OAAO,YA5NH,OAkOZ,WAAa,WAlOD,OAwOZ,SAAW"}