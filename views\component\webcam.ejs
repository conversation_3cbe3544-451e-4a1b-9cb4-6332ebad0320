<!-- webcam modal start -->
<!-- <div class="modal fade in" id="webCam" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="defaultModalLabel">Web camera</h4>
                <span class="float-right">
                <button type="button" class="btn form-submit-btn small-btn scan-btn passport-scan-bg-green passport-scan-text-color-white" data-dismiss="modal"  onclick="closeWebCam()"> <i class="fa fa-close" aria-hidden="true"></i> Close</button>
                <button type="button" id="takePhoto" class="btn form-submit-btn small-btn scan-btn passport-scan-bg-green passport-scan-text-color-white">
                    <i class="fa fa-camera" aria-hidden="true"></i> Capture
                </button>
                </span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div id="webCamera"></div>
                </div>
            </div>
        </div>
    </div>
</div> -->
<div class="webCamCapture d-none">
    <div class="row p-3" style="position: relative;z-index: *********;">
        <div class="col-6">
            <button type="button"
            class="btn form-submit-btn small-btn scan-btn passport-scan-bg-green passport-scan-text-color-white btn-camera-close"
            data-dismiss="modal" onclick="closeWebCam()"> <i class="fa fa-close m-0" aria-hidden="true"></i></button>
        </div>
        <div class="col-6 text-right">
            <button type="button" id="takePhoto"
            class="btn form-submit-btn small-btn scan-btn passport-scan-bg-green passport-scan-text-color-white btn-camera-capture">
            <i class="fa fa-camera m-0" aria-hidden="true"></i>
            </button>
        </div>
    </div>
    <div id="webCamera"></div>
</div>

<!-- <button id="start-camera">Start Camera</button> -->

<script>
    /* checkMediaAccess = async() => {
         navigator.mediaDevices.enumerateDevices().then( devices => 
             devices.forEach( device => {
                 if(device.kind == 'audioinput' && device.label) console.log('Has Audio Access');
                 if(device.kind == 'videoinput' && device.label) console.log('Has Video Access');
             }
         ));
     }
 /*document.querySelector("#start-camera").addEventListener('click', async function() {
     // suppose we require a full HD video
     let constraints = { 
                         audio: true, 
                         video: { 
                             width: { ideal: 1920 }, 
                             height: { ideal: 1080 } 
                         }
                     };
 
     let stream = await navigator.mediaDevices.getUserMedia(constraints);
 
     let stream_settings = stream.getVideoTracks()[0].getSettings();
 
     // actual width & height of the camera video
     let stream_width = stream_settings.width;
     let stream_height = stream_settings.height;
 
     console.log('Width: ' + stream_width + 'px');
     console.log('Height: ' + stream_height + 'px');
 }); */
    function doOnOrientationChange() {
        switch (window.orientation) {
            case -90: case 90:
                // landscape 
                return true;
                break;
            default:
                // portrait
                return false;
                break;
        }
    }
    function onChangeRotate() {
        switch (window.orientation) {
            case -90: case 90:
                // landscape 
                return true;
                break;
            default:
            closeWebCam();
                cuteAlert({
                    type: "warning",
                    title: "Camera",
                    message: 'Please rotate your phone landscap mode to capture image',
                    buttonText: "Ok"
                });
                break;
        }
    }

    window.addEventListener('orientationchange', onChangeRotate);



</script>
