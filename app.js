/*
 Author: <PERSON><PERSON><PERSON><PERSON>
 Description: define route, session, view and view engine, cors, route error handling
 */

 // import
const exppress = require('express');
const bodyParser = require('body-parser');
var path = require('path');
var cookieParser = require('cookie-parser');
var session = require('express-session');
const fileUpload = require('express-fileupload');
const cors = require('cors');
const dotenv = require('dotenv');
dotenv.config();
const app = exppress();
const sequelize = require('./config/database');
require('./config/sync'); 
const _ = require('lodash');

// access lodash from ejs inside
app.locals._ = _;

app.use(cookieParser());

// set session config
app.use(session({
  secret: "passport-scan-secret",
  resave: false,
  cookie: { maxAge: 2 * 60 * 60 * 1000 },
  saveUninitialized: false
}));

// set file upload config
app.use(fileUpload({
  limits: { fileSize: 100 * 1024 * 1024 }, // 10 mb
  abortOnLimit:true
}));

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

// set path for static assets
app.use(exppress.static(path.join(__dirname, 'public')));

// config cors and body parser
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

// get routes
const superAdminRoute = require('./v1/super-admin');
const apiRoute = require('./v1/api');
const publicRoute = require('./v1/public');
const routePermission = require('./v1/routing-permission');

// set routes
app.use('/', publicRoute);
app.use('/', routePermission);
app.use('/', superAdminRoute);
app.use('/v1/api', apiRoute);

// set path for static upload
app.use('/uploads',exppress.static(path.join(__dirname, 'uploads')));

 app.get('/privacy-policy', (req, res) => {
  res.sendFile(__dirname + '/views/template/privacy-policy.html'); // Replace with the actual path to your HTML file
});

// catch 404 and forward to error handler
app.use(function (req, res, next) {
  var err = new Error('Not Found');
  err.status = 404;
  next(err);
});

// error handler
app.use(function (err, req, res, next) {
  // render the error page
  // res.status(err.status || 500);
  console.log( 'message',err.message);
  const pageName = err.status === 404 ? 404 : 500;
  res.render("pages/error-"+pageName, { title: "Internal server error - 500", baseUrl: process.env.BASE_URL });
  // res.render('error', { status: err.status, message: err.message });
});

module.exports = app;
