RestError: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
 {
  "name": "RestError",
  "code": "401",
  "statusCode": 401,
  "request": {
    "streamResponseStatusCodes": {},
    "url": "https://samplescandocc.cognitiveservices.azure.com/formrecognizer/v2.1/custom/models/63aa7db0-8488-4cbd-8d48-b6a1c419ff68/analyze",
    "method": "POST",
    "headers": {
      "_headersMap": {
        "content-type": "image/jpeg",
        "accept": "application/json",
        "user-agent": "azsdk-js-ai-formrecognizer/3.2.0 core-http/2.2.4 Node/v14.17.0 OS/(x64-Darwin-17.7.0)",
        "x-ms-client-request-id": "34471bbf-680d-4e4c-835e-ad13a04d899b",
        "ocp-apim-subscription-key": "REDACTED",
        "cookie": "REDACTED"
      }
    },
    "withCredentials": false,
    "timeout": 0,
    "keepAlive": true,
    "requestId": "34471bbf-680d-4e4c-835e-ad13a04d899b"
  },
  "details": {
    "error": {
      "code": "401",
      "message": "Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource."
    }
  },
  "message": "Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource."
}
RestError: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
 {
  "name": "RestError",
  "code": "401",
  "statusCode": 401,
  "request": {
    "streamResponseStatusCodes": {},
    "url": "https://samplescandocc.cognitiveservices.azure.com/formrecognizer/v2.1/custom/models/63aa7db0-8488-4cbd-8d48-b6a1c419ff68/analyze",
    "method": "POST",
    "headers": {
      "_headersMap": {
        "content-type": "image/jpeg",
        "accept": "application/json",
        "user-agent": "azsdk-js-ai-formrecognizer/3.2.0 core-http/2.2.4 Node/v14.17.0 OS/(x64-Darwin-17.7.0)",
        "x-ms-client-request-id": "6f1fc7df-4ebf-4983-a288-3d84b01796f8",
        "ocp-apim-subscription-key": "REDACTED",
        "cookie": "REDACTED"
      }
    },
    "withCredentials": false,
    "timeout": 0,
    "keepAlive": true,
    "requestId": "6f1fc7df-4ebf-4983-a288-3d84b01796f8"
  },
  "details": {
    "error": {
      "code": "401",
      "message": "Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource."
    }
  },
  "message": "Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource."
}
RestError: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
 {
  "name": "RestError",
  "code": "401",
  "statusCode": 401,
  "request": {
    "streamResponseStatusCodes": {},
    "url": "https://samplescandocc.cognitiveservices.azure.com/formrecognizer/v2.1/custom/models/63aa7db0-8488-4cbd-8d48-b6a1c419ff68/analyze",
    "method": "POST",
    "headers": {
      "_headersMap": {
        "content-type": "image/jpeg",
        "accept": "application/json",
        "user-agent": "azsdk-js-ai-formrecognizer/3.2.0 core-http/2.2.4 Node/v14.17.0 OS/(x64-Darwin-17.7.0)",
        "x-ms-client-request-id": "f7cc46b9-fc05-4ec3-9b68-3714e718108a",
        "ocp-apim-subscription-key": "REDACTED",
        "cookie": "REDACTED"
      }
    },
    "withCredentials": false,
    "timeout": 0,
    "keepAlive": true,
    "requestId": "f7cc46b9-fc05-4ec3-9b68-3714e718108a"
  },
  "details": {
    "error": {
      "code": "401",
      "message": "Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource."
    }
  },
  "message": "Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource."
}
