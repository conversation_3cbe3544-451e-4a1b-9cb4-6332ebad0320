<%- include ('../template/header') -%>
    <%- include ('../template/navbar') -%>
        <div class="container-fluid container-fluid-width mt-3">
            <div class="row">
                <div class="col-6">
                    <h2 class="heading-bold m-0">User management</h2>
                </div>
                <div class="col-6 text-right">
                    <% if(permission[0].create==='Y' ){ %>
                        <div class="form-group m-0">
                            <a href="<%- baseUrl -%>user/create"
                                class="btn scan-btn small-btn passport-scan-bg-green passport-scan-text-color-white"><i
                                    class="fa fa-plus"></i> Create</a>
                        </div>
                        <% } %>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width mt-3 passport-scan-bg-white br-15 p-4">
            <div class="row">
                <div class="col-md-12 d-flex justify-content-end mb-2">
                    <div class="form-group mr-2">
                        <select id="show-record" class="form-control no-ob">
                            <option value="10">10</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="search" id="search" class="form-control no-ob" placeholder="Search">
                    </div>
                    <div class="form-group  ml-3">
                        <button type="button"
                            class="btn scan-btn btn-search passport-scan-bg-green passport-scan-text-color-white">Search</button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="user-table" data-classes="table">
                            <thead>
                                <tr>
                                    <th data-field="id">#</th>
                                    <th data-field="property" data-sortable="true">Property</th>
                                    <th data-field="firstName" data-sortable="true">First name</th>
                                    <th data-field="lastName" data-sortable="true">Last name</th>
                                    <th data-field="email" data-sortable="true">Email</th>
                                    <th data-field="phone" data-sortable="true">Phone number</th>
                                    <th data-field="date" data-sortable="true">Created date</th>
                                    <th data-field="status" data-sortable="true">Status</th>
                                    <th data-field="action">Action</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width">
            <div class="row">
                <div class="col-md-12">
                    <div class="loadPagination row  mt-4 mb-5"></div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            var pagePermission = JSON.parse(`<%- JSON.stringify(permission[0]) -%>`);
            var url = new URL(window.location.href);
            var searchParams = new URLSearchParams(url.search);
            $(document).ready(function () {
                searchParams.get('per-page') ? $('#show-record').val(searchParams.get('per-page')) : '';

                getList();
                $('.btn-search').click(function () {
                    getList();
                });

                $('#show-record').change(function () {
                    window.location.href = `${baseUrl}user?per-page=${$('option:selected', this).val()}`;
                    getList();
                });
            });
            function getList() {
                var page = searchParams.get('page');
                var pageId = page ? `&page=${page}` : '';
                var beforeQ = page ? '&' : '&';
                var search = $('#search').val() !== "" ? (beforeQ + encodeURI(`search=${$('#search').val()}`)) : '';

                $.ajax({
                    url: `${apiUrl}user?total-page=${$('#show-record option:selected').val()}${pageId}${search}`,
                    method: 'get',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    },
                    success: function (res) {
                        if (res.status === true) {
                            var myData = [];

                            $.each(res.data, function (ind, row) {
                                var _editBtn = pagePermission.edit === 'Y' ? `<a class="mr-2" href="<%- baseUrl -%>user/edit/${row.user_id}"><i class="fa fa-pencil"></i></a>` : '';
                                var _deleteBtn = pagePermission.edit === 'Y' ? `<a class="text-danger" href="javascript:;" onclick="deleteUser(${row.user_id})"><i class="fa fa-trash"></i></a>` : '';
                                myData.push({
                                    "id": (parseInt(res.offset) + parseInt(ind + 1)),
                                    "property": row.property_name,
                                    "firstName": row.first_name,
                                    "lastName": row.last_name,
                                    "email": row.email,
                                    "phone": '+'+row.phone,
                                    "date": row.created_at,
                                    "status": row.is_active === 'Y' ? '<span class="badge badge-success text-small">Active</span>' : '<span class="badge badge-secondary text-small">Deactived</span>',
                                    "action": `${_editBtn} ${_deleteBtn}`
                                });
                            });
                            $('table').bootstrapTable('destroy');
                            $('table').bootstrapTable({
                                data: myData
                            });

                            bindPagination(page, res.totalPages, 'user');

                        }
                    }
                });
            }
            function deleteUser(id) {
                cuteAlert({
                    type: "question",
                    title: "Delete the user",
                    message: "Are you sure do you want to delete?",
                    confirmText: "Yes",
                    cancelText: "No"
                }).then((e) => {
                    if (e == 'confirm') {
                        $.ajax({
                            url: `${apiUrl}user/${id}`,
                            type: 'delete',
                            dataType: 'json',
                            headers: {
                                "Authorization": "Bearer <%- userDetails.token -%>"
                            },
                            success: function (res) {
                                cuteAlert({
                                    type: res.status ? "success" : "error",
                                    title: "Delete user",
                                    message: res.msg,
                                    buttonText: "Ok"
                                }).then(() => {
                                    res.status ? getList() : null;
                                })
                            }, complete: function () {

                            }
                        })
                    }
                })
            }
        </script>
        <%- include ('../template/footer') -%>