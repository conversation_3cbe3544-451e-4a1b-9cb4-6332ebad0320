// controllers/bookingDetailsController.js
const BookingDetails = require('../models/bookingDetails');
const { Op } = require('sequelize');
const moment = require('moment');

module.exports = {
  create: async function (req, res) {
    const { booking_id, first_name, last_name, date_of_birth, created_by, is_active } = req.body;
    let currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss');

    try {
      const newBookingDetail = await BookingDetails.create({
        booking_id,
        first_name,
        last_name,
        date_of_birth: date_of_birth || currentDateTime,
        created_by,
        created_at: currentDateTime,
        is_active
      });
      res.status(201).json({ status: true, message: "Booking detail successfully created", data: newBookingDetail });
    } catch (error) {
      console.error(error);
      res.status(500).json({ status: false, message: "Error creating booking detail", error });
    }
  },

  list: async function (req, res) {
    const { from_date, to_date, search_term } = req.query;

    const whereConditions = {};
    if (from_date) {
      whereConditions.created_at = { [Op.gte]: new Date(from_date) };
    }
    if (to_date) {
      if (!whereConditions.created_at) {
        whereConditions.created_at = {};
      }
      whereConditions.created_at[Op.lte] = new Date(to_date);
    }
    if (search_term) {
      whereConditions[Op.or] = [
        { booking_id: { [Op.like]: `%${search_term}%` } },
        { first_name: { [Op.like]: `%${search_term}%` } },
        { last_name: { [Op.like]: `%${search_term}%` } },
      ];
    }


    try {
      const bookingDetails = await BookingDetails.findAll({
        where: whereConditions,
      });
      res.status(200).json({ status: true, data: bookingDetails });
    } catch (error) {
      console.error(error);
      res.status(500).json({ status: false, message: "Error fetching booking details", error });
    }
  }

  
};
