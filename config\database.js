// config/database.js
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize('vownix', 'root', 'Vrdella!7', {
  host: 'localhost',
  dialect: 'mysql',
  logging: console.log, // Enable logging to see SQL queries
});

sequelize.authenticate()
  .then(() => {
    console.log('Connection has been established successfully.');
  })
  .catch(err => {
    console.error('Unable to connect to the database:', err);
  });

module.exports = sequelize;
